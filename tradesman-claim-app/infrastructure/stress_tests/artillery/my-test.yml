config:
  target: 'https://claim-app.rrcpglobal.com/api' # The base URL of the service you want to test
  phases:
    - duration: 60  # Duration of the test in seconds
      arrivalRate: 10 # Number of new virtual users per second
      rampTo: 50    # Gradually increase arrivalRate to 50 over the duration
      name: "Ramp up load"
    - duration: 120 # Duration of the steady load phase
      arrivalRate: 50 # Steady number of new virtual users per second
      name: "Steady load"

scenarios:
  - name: "Get all posts"
    flow:
      - get:
          url: "/"
          # Expected status codes
          expect:
            statusCode: 200
      - get:
          url: "/health"
          expect:
            statusCode: 200
      - post:
          url: "/auth/signin"
          json:
            email: "<EMAIL>"
            password: "123123123"            
          expect:
            statusCode: 200