import http from 'k6/http';
import { sleep, check } from 'k6';

export const options = {
  stages: [
    { duration: '1m', target: 20 },  // Ramp up to 50 VUs (virtual users) over 1 minute
    { duration: '3m', target: 40 }, // Ramp up to 200 VUs over 3 minutes (stress phase)
    { duration: '5m', target: 100 }, // Stay at 200 VUs for 5 minutes
    { duration: '1m', target: 0 },   // Ramp down to 0 VUs over 1 minute
  ],
  thresholds: {
    'http_req_duration{scenario:default}': ['p(95)<500'], // 95th percentile of request duration should be below 500ms
    'http_req_failed': ['rate<0.01'], // less than 1% of requests should fail
  },
};

export default function () {
  const res = http.get('https://claim-app.rrcpglobal.com/api/health'); // Replace with your target URL
  check(res, {
    'is status 200': (r) => r.status === 200,
  });
  sleep(1); // Simulate user thinking time
}