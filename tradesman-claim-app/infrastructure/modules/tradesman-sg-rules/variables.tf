variable "security_group_id"{
  type = string
}
variable "cidr_blocks" {
  type    = list(string)
  default = [
      #   "***********/24",
      # "10.0.0.0/16",
      # "**********/12"
  ]
}

variable "ingress_ports" {
  type = list(object({
    protocol  = string
    from_port = number
    to_port   = number
  }))
  default = [
    # {
    #   protocol  = "tcp"
    #   from_port = 80
    #   to_port   = 80
    # },
    # {
    #   protocol  = "tcp"
    #   from_port = 443
    #   to_port   = 443
    # }
  ]
}
variable "ingress_rules" {
  type = list(object({
    cidr_ipv4 = list(string)  # Changed from string to list(string)
    from_port = number
    to_port   = number
    protocol  = string
  }))
  description = "List of ingress rules with multiple CIDR blocks per rule."

  # Example default value with multiple CIDR blocks
  default = [
    # {
    #   cidr_ipv4 = ["10.0.0.0/24", "********/24"]  # List of CIDR blocks
    #   from_port = 80
    #   to_port   = 80
    #   protocol  = "tcp"
    # },
    # {
    #   cidr_ipv4 = ["***********/24"]  # Single CIDR block
    #   from_port = 443
    #   to_port   = 443
    #   protocol  = "tcp"
    # }
  ]
}
variable "ingress_rules_reference" {
  type = list(object({
    reference_security = list(string)  # List of security group references
    from_port = number
    to_port   = number
    protocol  = string

  }))
  default = []
}
variable "egress_ports" {
  type = list(object({
    protocol  = string
    from_port = number
    to_port   = number
  }))
  default = []  # Default to no egress rules
  description = "The list of ports to expose within the security groups."
}

variable "ip_protocol" {
  type    = string
  default = "tcp"  # Default to TCP protocol, you can choose any protocol or -1 for all protocols
}

variable "ipv6_cidr_blocks" {
  type        = list(string)
  default     = []  # Default to no IPv6 CIDR blocks
  description = "The list of IPv6 CIDR blocks to associate with the security groups."
}

variable "apply_all_cidrs" {
  type        = bool
  description = "Flag to indicate whether to apply all CIDRs to all rules or not."
  default     = false
}
variable "tags" {
  type        = map(string)
  default     = {}  # Default to no tags
  description = "Additional tags associated with the product / project."
}


