locals {
ingress_rules_map = {
  for cidr in var.cidr_blocks : cidr => {
    for rule in var.ingress_ports : 
    "${cidr}-${rule.from_port}-${rule.to_port}-${rule.protocol}" => {
      ip_protocol = rule.protocol
      from_port   = rule.from_port
      to_port     = rule.to_port
      cidr_ipv4   = cidr
    }
  }
}
 # Flatten the ingress rules into a single list of maps
  flattened_ingress_rules_list = flatten([
    for cidr, rules in local.ingress_rules_map : [
      for k, v in rules : {
        cidr_block = cidr
        from_port  = v.from_port
        to_port    = v.to_port
        protocol   = v.ip_protocol
        # key        = k
        # value      = v
      }
    ]
  ])

  # Convert the list of maps into a single map
  flattened_ingress_rules_map = {
    for rule in local.flattened_ingress_rules_list : 
    "${rule.cidr_block}-${rule.from_port}-${rule.to_port}-${rule.protocol}" => {
      cidr_ipv4   = rule.cidr_block
      from_port   = rule.from_port
      ip_protocol = rule.protocol
      to_port     = rule.to_port
    }
  }
  # Fallback CIDR map if no CIDR blocks are provided
 fallback_cidr = length(var.cidr_blocks) > 0 ? local.flattened_ingress_rules_map : {
    for rule in var.ingress_ports : 
    "0.0.0.0/0-${rule.from_port}-${rule.to_port}-${rule.protocol}" => {
      cidr_ipv4   = "0.0.0.0/0"
      from_port   = rule.from_port
      ip_protocol = rule.protocol
      to_port     = rule.to_port
    }
  }

 #Flatten the ingress rules to handle each combination of CIDR block, port, and protocol
  flattened_ingress_rules = flatten([
    for rule in var.ingress_rules : [
      for cidr in rule.cidr_ipv4 : {
        cidr_block = cidr
        from_port  = rule.from_port
        to_port    = rule.to_port
        protocol   = rule.protocol
      }
    ]
  ])
# Flatten the ingress rules based on security group references
  flattened_ingress_rules_reference = flatten([
    for rule in var.ingress_rules_reference : [
      for ref in rule.reference_security : {
        security_group = ref
        from_port      = rule.from_port
        to_port        = rule.to_port
        protocol       = rule.protocol
      }
    ]
  ])
default_tags =  {
      Project = "Tradesman"
      Name        = "Terraform"
      App = "Claim-App"
    }
  
}

# resource "aws_security_group" "main" {
#   name        = var.name
#   description = "example"
#   vpc_id      = "vpc-0f41b716ae66eb22d"
#   tags = merge(local.default_tags,var.tags)

# }
resource "aws_vpc_security_group_ingress_rule" "ingress_all" {
  for_each = local.fallback_cidr 

  security_group_id = var.security_group_id

  cidr_ipv4   = each.value.cidr_ipv4
  from_port   = each.value.from_port
  to_port     = each.value.to_port
  ip_protocol = each.value.ip_protocol
  #referenced_security_group_id = var.referenced_security_group_id
  tags = merge(local.default_tags,var.tags)
}


resource "aws_vpc_security_group_ingress_rule" "ingress" {
  for_each = { 
    for idx, rule in local.flattened_ingress_rules : 
    "${rule.cidr_block}-${rule.from_port}-${rule.to_port}-${rule.protocol}" => rule 
  }

 security_group_id = var.security_group_id

  cidr_ipv4   = each.value.cidr_block # Change to cidr_ipv4 if necessary
  from_port   = each.value.from_port
  to_port     = each.value.to_port
  ip_protocol = each.value.protocol

  # Optional Arguments - Add them as needed:
  # cidr_ipv6               = "YOUR_IPV6_CIDR_BLOCK"
  # description             = "Your rule description here"
  # prefix_list_id          = "YOUR_PREFIX_LIST_ID"
  #referenced_security_group_id = var.referenced_security_group_id
  
   tags = merge(local.default_tags,var.tags)
}
resource "aws_security_group_rule" "ingress_reference" {
  count = length(local.flattened_ingress_rules_reference)

  type                  = "ingress"
  from_port             = local.flattened_ingress_rules_reference[count.index].from_port
  to_port               = local.flattened_ingress_rules_reference[count.index].to_port
  protocol              = local.flattened_ingress_rules_reference[count.index].protocol
  source_security_group_id = local.flattened_ingress_rules_reference[count.index].security_group
  security_group_id = var.security_group_id
}


resource "aws_vpc_security_group_egress_rule" "example" {
  security_group_id = var.security_group_id
  cidr_ipv4 = "0.0.0.0/0"
  ip_protocol = -1
 
}