# tradesman-aws-asg module

# Create Launch Template
resource "aws_launch_template" "this" {
  name_prefix   = "${var.name_prefix}-lt-"
  image_id      = var.image_id
  instance_type = var.instance_type
  key_name      = var.key_name
  user_data     = var.user_data != "" ? base64encode(var.user_data) : null
  
  iam_instance_profile {
    name = var.iam_instance_profile
  }
  
  vpc_security_group_ids = var.security_group_ids
  
  block_device_mappings {
    device_name = "/dev/sda1"
    
    ebs {
      volume_size = var.volume_size
      volume_type = "gp3"
      encrypted   = true
    }
  }
  
  tag_specifications {
    resource_type = "instance"
    
    tags = merge(
      var.tags,
      {
        Name = "${var.name_prefix}-instance"
      }
    )
  }
  
  lifecycle {
    create_before_destroy = true
  }
}

# Create target group
resource "aws_lb_target_group" "this" {
  name     = "${var.name_prefix}-tg"
  port     = 80
  protocol = "HTTP"
  vpc_id   = var.vpc_id

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30          # Reduced from 60 to detect changes faster
    timeout             = 10          # Increased from 5 to allow more time for response
    path                = "/health"
    port                = "traffic-port"
    unhealthy_threshold = 3
    matcher             = "200-299"   # Accept any 2xx response as healthy
  }

  # Add stickiness if needed
  stickiness {
    enabled = true
    type    = "lb_cookie"
    cookie_duration = 86400
  }
}

# Create Application Load Balancer if requested
resource "aws_lb" "this" {
  count              = var.create_lb ? 1 : 0
  name               = "${var.name_prefix}-alb"
  internal           = var.lb_internal
  load_balancer_type = "application"
  security_groups    = var.lb_security_groups
  subnets            = var.subnet_ids

  enable_deletion_protection = false

  tags = var.tags
}

# Create ALB listener
resource "aws_lb_listener" "http" {
  count             = var.create_lb ? 1 : 0
  load_balancer_arn = aws_lb.this[0].arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.this.arn
  }
}

# Create Auto Scaling Group
resource "aws_autoscaling_group" "this" {
  name                = "${var.name_prefix}-asg"
  desired_capacity    = var.desired_capacity
  max_size            = var.max_size
  min_size            = var.min_size
  target_group_arns   = [aws_lb_target_group.this.arn]
  vpc_zone_identifier = var.subnet_ids
  health_check_type   = var.health_check_type
  health_check_grace_period = 300

  launch_template {
    id      = aws_launch_template.this.id
    version = "$Latest"
  }

  dynamic "tag" {
    for_each = var.tags
    content {
      key                 = tag.key
      value               = tag.value
      propagate_at_launch = true
    }
  }
}

# Add scaling policies
resource "aws_autoscaling_policy" "scale_up" {
  name                   = "${var.name_prefix}-scale-up"
  autoscaling_group_name = aws_autoscaling_group.this.name
  adjustment_type        = "ChangeInCapacity"
  scaling_adjustment     = 1
  cooldown               = 300
  policy_type            = "SimpleScaling"
}

resource "aws_autoscaling_policy" "scale_down" {
  name                   = "${var.name_prefix}-scale-down"
  autoscaling_group_name = aws_autoscaling_group.this.name
  adjustment_type        = "ChangeInCapacity"
  scaling_adjustment     = -1
  cooldown               = 300
  policy_type            = "SimpleScaling"
}

# Output the load balancer DNS name
output "lb_dns_name" {
  value       = var.create_lb ? aws_lb.this[0].dns_name : null
  description = "The DNS name of the load balancer"
}
