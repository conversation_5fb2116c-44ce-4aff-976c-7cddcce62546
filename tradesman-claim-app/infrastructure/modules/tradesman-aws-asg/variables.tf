variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "image_id" {
  description = "AMI ID for the instances"
  type        = string
}

variable "instance_type" {
  description = "Instance type for the instances"
  type        = string
}

variable "key_name" {
  description = "SSH key name for the instances"
  type        = string
}

variable "security_group_ids" {
  description = "Security group IDs for the instances"
  type        = list(string)
}

variable "vpc_id" {
  description = "VPC ID where resources will be created"
  type        = string
}

variable "subnet_ids" {
  description = "Subnet IDs where instances will be launched"
  type        = list(string)
}

variable "user_data" {
  description = "User data script for the instances"
  type        = string
  default     = ""
}

variable "volume_size" {
  description = "Size of the root volume in GB"
  type        = number
  default     = 20
}

variable "desired_capacity" {
  description = "Desired number of instances in the ASG"
  type        = number
  default     = 1
}

variable "min_size" {
  description = "Minimum number of instances in the ASG"
  type        = number
  default     = 1
}

variable "max_size" {
  description = "Maximum number of instances in the ASG"
  type        = number
  default     = 3
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "create_lb" {
  description = "Whether to create a load balancer"
  type        = bool
  default     = false
}

variable "lb_internal" {
  description = "Whether the load balancer is internal"
  type        = bool
  default     = false
}

variable "lb_security_groups" {
  description = "Security group IDs for the load balancer"
  type        = list(string)
  default     = []
}

variable "health_check_type" {
  description = "Type of health check to perform (EC2 or ELB)"
  type        = string
  default     = "ELB"
}

variable "iam_instance_profile" {
  description = "IAM instance profile name for the instances"
  type        = string
  default     = null
}
