# Load user-data template and pass variables dynamically
data "template_file" "backend_user_data" {
  template = file("${path.module}/scripts/backend_userdata.tpl")
}
data "template_file" "frontend_user_data" {
  template = file("${path.module}/scripts/frontend_userdata.tpl")
}

module "vpc" {
    source = "../../../modules/tradesman-aws-vpc"
    name = "Tradesman"
    no_of_azs = 2
    tags = {
    "Environment" = "Dev"
    "Department"  = "DevOps"
    "Project"     = "Tradesman"
    }
}
module "sg"{
  source ="../../../modules/tradesman-sg"
  security_groups = [
    {
      name        = "dev-claim-app-frontend-sg"
      description = "Security group for claim app frontend servers"
      vpc_id      = module.vpc.id
      tags        = {
        Name = "dev-claim-app-frontend-sg"
        Environment = "Development"
      }
    },
    {
      name        = "dev-claim-app-db-sg"
      description = "Security group for claim app db servers"
      vpc_id      =  module.vpc.id
      tags        = {
        Name = "dev-claim-app-db-sg"
        Environment = "Development"
      }
    },
    {
      name        = "dev-claim-app-backend-sg"
      description = "Security group for claim app backend servers"
      vpc_id      = module.vpc.id
      tags        = {
        Name = "dev-claim-app-backend-sg"
        Environment = "Development"
      }
    }

  ]
}
module "sg-rules-1" {
  source = "../../../modules/tradesman-sg-rules"
  security_group_id = lookup(module.sg.security_group_ids, "dev-claim-app-frontend-sg")
  ingress_rules = [
    {
      cidr_ipv4 = ["0.0.0.0/0"]  # List of CIDR blocks
      from_port = 22
      to_port   = 22
      protocol  = "tcp"
    },
    {
      cidr_ipv4 = ["0.0.0.0/0"]  # List of CIDR blocks
      from_port = 80
      to_port   = 80
      protocol  = "tcp"
    },
    {
      cidr_ipv4 = ["0.0.0.0/0"]  # List of CIDR blocks
      from_port = 443
      to_port   = 443
      protocol  = "tcp"
    }
  ]
}

module "sg-rules-2" {
  source = "../../../modules/tradesman-sg-rules"
  security_group_id = lookup(module.sg.security_group_ids, "dev-claim-app-db-sg")
  ingress_rules_reference = [
    {
      reference_security = [lookup(module.sg.security_group_ids, "dev-claim-app-backend-sg"),lookup(module.sg.security_group_ids, "dev-claim-app-frontend-sg")]  # Example security group reference
      from_port = 3306
      to_port   = 3306
      protocol  = "tcp"
    }
  ]
}
module "sg-rules-3" {
  source = "../../../modules/tradesman-sg-rules"
  security_group_id = lookup(module.sg.security_group_ids, "dev-claim-app-backend-sg")
  ingress_rules_reference = [
    {
      reference_security = [lookup(module.sg.security_group_ids, "dev-claim-app-frontend-sg")]  # Allow frontend sg in backend sg
      from_port = 22
      to_port   = 22
      protocol  = "tcp"
    },
        {
      reference_security = [lookup(module.sg.security_group_ids, "dev-claim-app-frontend-sg")]  # Example security group reference
      from_port = 80
      to_port   = 80
      protocol  = "tcp"
    },

  ]
}

module "mysql-dev" {
  source = "../../../modules/tradesman-aws-rds"
  identifier = "dev-tradesman-claim-app-db"
  engine               = "mysql"
  engine_version       = "8.0"
  family               = "mysql8.0" 
  major_engine_version = "8.0"      
  instance_class       = "db.t3.micro"
  create_db_subnet_group = true
  allocated_storage = 20
  db_name  = "claimappdb"
  username = "admin_claimappdb"
  port     = 3306
  subnet_ids             =  module.vpc.private_subnets
  vpc_security_group_ids = [lookup(module.sg.security_group_ids, "dev-claim-app-db-sg")]
  backup_retention_period = 0
  create_db_option_group = false
  create_db_parameter_group = true

}
module "claim-app-frontend" {
  source  = "../../../modules/tradesman-ec2"
  key_name              = aws_key_pair.generated_key.id
  name                 = "dev-claim-app-frontend"
  instance_type         = "t2.small"
  ami                   = "ami-0e86e20dae9224db8" 
  monitoring            = false
  vpc_security_group_ids = [lookup(module.sg.security_group_ids, "dev-claim-app-frontend-sg")]
  subnet_id             = module.vpc.public_subnets[0]
  associate_public_ip_address = true
  ebs_block_device = [
    {
      device_name           = "/dev/sda1" 
      volume_size           = 50          
      volume_type           = "gp2"       
      delete_on_termination = true         
    }
  ]
  user_data = data.template_file.frontend_user_data.rendered
}
module "claim-app-backend" {
  source  = "../../../modules/tradesman-ec2"
  key_name              = aws_key_pair.generated_key.id
  name                 = "dev-claim-app-backend"
  instance_type         = "t2.small"
  ami                   = "ami-0e86e20dae9224db8" 
  monitoring            = false
  vpc_security_group_ids = [lookup(module.sg.security_group_ids, "dev-claim-app-backend-sg")]
  subnet_id             = module.vpc.private_subnets[0]
  associate_public_ip_address = false
  ebs_block_device = [
    {
      device_name           = "/dev/sda1" 
      volume_size           = 50          
      volume_type           = "gp2"       
      delete_on_termination = true         
    }
  ]
  user_data = data.template_file.backend_user_data.rendered
}

resource "aws_eip_association" "frontend_eip_assoc" {
  instance_id   = module.claim-app-frontend.id
  allocation_id = aws_eip.frontend.id
}



output "security_group_ids" {
  value = module.sg.security_group_ids
  description = "Map of security group names to their IDs from the sg module."
}
output "private_subnets" {
  value = module.vpc.*.private_subnets
  description = "Map of security group names to their IDs from the sg module."
}
