#!/bin/bash
set -e

# Update package list
sudo apt update -y

# Install NGINX
sudo apt install nginx -y

# Enable and start NGINX service
sudo systemctl enable nginx
sudo systemctl start nginx


# Create the Nginx configuration for tradesman-app
cat << 'EOF' | sudo tee /etc/nginx/sites-available/tradesman-app.conf
server {
    listen 80;  # Change to listen 443 if using HTTPS
    server_name ;  # Replace with your domain name or server IP

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;

        # Optional: Additional headers
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    error_page 500 502 503 504 /50x.html;  # Custom error pages
    location = /50x.html {
        root /usr/share/nginx/html;  # Default error page location
    }
}
EOF

# Enable the tradesman-app configuration
sudo ln -s /etc/nginx/sites-available/tradesman-app.conf /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# Restart Nginx to apply changes
sudo systemctl restart nginx

# Create the destination folder for NVM under /usr/local/nvm
mkdir -p /usr/local/nvm

# Install NVM (Node Version Manager) - Use a more recent version of NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | NVM_DIR=/usr/local/nvm bash

# Create the nvm.sh script in /etc/profile.d to make NVM available globally
cat << 'EOF' | sudo tee /etc/profile.d/nvm.sh
#!/usr/bin/env bash
export NVM_DIR="/usr/local/nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
EOF

# Set permissions for nvm.sh
sudo chmod 755 /etc/profile.d/nvm.sh

# Install Node.js using NVM (this must be run in a login shell)
sudo -i -u ubuntu bash << EOF
    source /etc/profile.d/nvm.sh
    nvm install node
EOF

# Install PM2 globally
sudo -i -u ubuntu bash << EOF
    source /etc/profile.d/nvm.sh
    npm install -g pm2
EOF


# Verify installations
echo "NVM and Node.js versions:"
nvm --version
node --version
