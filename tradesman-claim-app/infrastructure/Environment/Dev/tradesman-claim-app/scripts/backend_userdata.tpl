#!/bin/bash

# Update the package repository and install necessary packages
apt-get update -y
apt-get install build-essential libssl-dev curl unzip zip php php-cli php-curl php-zip php-fpm php-xml php-mysql php-mbstring  -y


# Remove Apache if installed
if dpkg -l | grep -q apache2; then
    echo "Removing Apache2..."
    systemctl stop apache2
    apt-get remove apache2 -y
    apt purge apache2 -y 
    rm -rf /usr/sbin/apache2
    rm -rf /etc/apache2/
fi
apt install nginx -y
# Enable and start PHP-FPM service
systemctl enable php8.3-fpm.service
systemctl start php8.3-fpm.service
sudo systemctl enable nginx
sudo systemctl start nginx
cat << 'EOF' | sudo tee /etc/nginx/sites-available/tradesman-backend.conf
server {
    listen 80;
    listen [::]:80;
    server_name _;
    root /home/<USER>/tradesman-claim-app-backend/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/run/php/php-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
EOF

# Enable the tradesman-app configuration
sudo ln -s /etc/nginx/sites-available/tradesman-backend.conf /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# Restart Nginx to apply changes
sudo systemctl restart nginx

# Verify the installed PHP version
php -v

# Create the destination folder for NVM under /usr/local/nvm
mkdir -p /usr/local/nvm

# Install NVM (Node Version Manager) - Use a more recent version of NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | NVM_DIR=/usr/local/nvm bash

# Create the nvm.sh script in /etc/profile.d to make NVM available globally
cat << 'EOF' | sudo tee /etc/profile.d/nvm.sh
#!/usr/bin/env bash
export NVM_DIR="/usr/local/nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
EOF

# Set permissions for nvm.sh
sudo chmod 755 /etc/profile.d/nvm.sh

# Source NVM to make it available in the current session
source /etc/profile.d/nvm.sh

# Install Node.js using NVM
nvm install node

# Make NVM and Node.js available to the ubuntu user as well
sudo -i -u ubuntu bash << EOF
    export NVM_DIR="/usr/local/nvm"
    [ -s "\$NVM_DIR/nvm.sh" ] && \. "\$NVM_DIR/nvm.sh"
    nvm install node
    nvm use node
EOF

# Install Composer (PHP Dependency Manager) with checksum verification
export COMPOSER_HOME="/home/<USER>/.config/composer"
EXPECTED_CHECKSUM="$(php -r 'copy("https://composer.github.io/installer.sig", "php://stdout");')"
php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
ACTUAL_CHECKSUM="$(php -r "echo hash_file('sha384', 'composer-setup.php');")"

if [ "$EXPECTED_CHECKSUM" != "$ACTUAL_CHECKSUM" ]; then
    >&2 echo 'ERROR: Invalid installer checksum'
    rm composer-setup.php
    exit 1
fi

php composer-setup.php --quiet
RESULT=$?
rm composer-setup.php
mv composer.phar /usr/local/bin/composer

# Verify installation versions of PHP, Node.js, and Composer
php -v
node -v
composer -v
