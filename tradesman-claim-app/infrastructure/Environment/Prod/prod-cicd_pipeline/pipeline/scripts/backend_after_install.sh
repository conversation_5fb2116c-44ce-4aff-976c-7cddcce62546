#!/bin/bash
cd /home/<USER>/tradesman-claim-app-backend

# Install PHP dependencies
composer install --no-dev --optimize-autoloader

# Set proper permissions
chmod -R 775 storage bootstrap/cache

# Set up environment
# cp .env.example .env
cat << 'EOF' | sudo tee /etc/nginx/sites-available/tradesman-backend.conf
APP_NAME="Tradesman Claim App"
APP_ENV=local
APP_KEY=base64:RWtUIbFdz5xVw729fYU6gm5HWW5XSoaqeg4CesIZOEg=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost
FRONTEND_APP_URL="prod-claim-app-frontend-alb-169970148.us-east-1.elb.amazonaws.com"
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US
APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database
BCRYPT_ROUNDS=12
LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug
DB_CONNECTION=mysql
DB_HOST=prod-tradesman-claim-app-db.c3gi6kum2hih.us-east-1.rds.amazonaws.com
DB_PORT=3306
DB_DATABASE=claim_app
DB_USERNAME=admin
DB_PASSWORD='Ge}gsjXy)iR%gq:i'
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null
BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
CACHE_STORE=database
CACHE_PREFIX=
MEMCACHED_HOST=127.0.0.1
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=**************************************************
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false
VITE_APP_NAME="${APP_NAME}"
EOF
php artisan key:generate
php artisan config:cache
php artisan route:cache
php artisan view:cache