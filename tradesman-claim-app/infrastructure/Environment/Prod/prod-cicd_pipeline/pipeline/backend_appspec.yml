version: 0.0
os: linux
files:
  - source: /
    destination: /home/<USER>/tradesman-claim-app-backend
    overwrite: yes

permissions:
  - object: /home/<USER>/tradesman-claim-app-backend
    owner: ubuntu
    group: ubuntu
    mode: 755

hooks:
  BeforeInstall:
    - location: scripts/backend_before_install.sh
      timeout: 300
      runas: root
  AfterInstall:
    - location: scripts/backend_after_install.sh
      timeout: 300
      runas: ubuntu
  # ApplicationStart:
  #   - location: scripts/backend_start_application.sh
  #     timeout: 300
  #     runas: ubuntu
  # ApplicationStop:
  #   - location: scripts/backend_stop_application.sh
  #     timeout: 300
  #     runas: ubuntu