version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 16
    commands:
      - echo Installing dependencies...
      - apt-get update -y
      - apt-get install -y zip unzip

  pre_build:
    commands:
      - echo Preparing source code...

  build:
    commands:
      - echo Creating artifact package...
      - zip -r application.zip .

  post_build:
    commands:
      - echo Source packaging completed

artifacts:
  files:
    - application.zip
    - appspec.yml
    - scripts/**/*
  discard-paths: no