version: 0.0
os: linux
files:
  - source: /
    destination: /home/<USER>/tradesman-claim-app-frontend
    overwrite: yes

permissions:
  - object: /home/<USER>/tradesman-claim-app-frontend
    owner: ubuntu
    group: ubuntu
    mode: 755

hooks:
  BeforeInstall:
    - location: scripts/frontend_before_install.sh
      timeout: 300
      runas: root
  AfterInstall:
    - location: scripts/frontend_after_install.sh
      timeout: 300
      runas: ubuntu
  ApplicationStart:
    - location: scripts/frontend_start_application.sh
      timeout: 300
      runas: ubuntu
  ApplicationStop:
    - location: scripts/frontend_stop_application.sh
      timeout: 300
      runas: ubuntu