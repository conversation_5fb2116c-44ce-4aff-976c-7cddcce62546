
1-- create ec2 role + policies
2-- create deploy roled
3-- install codedeploy agent on ec2, updat user data script
4- front app ssl
5- update cicd stages 
6- save artifacts as .zip with timestamp suffix
7- enable ec2 monitoring + alerts

Backend:
1- ASG + LB with 1 desired instance
2- Disk space 10g (gp2 disk)
3- npm + node
4- react server on 300 + nginx
5- WAF


Backend:
1- ASG + LB with 1 desired instance
2- Disk space 20g (gp2 disk)
3- nginx + php-fpm



Mysql DB:

1- RDS instance , Production, multiAZ(2 instances)
2- instance type m7g.large
3- disk space 10g (gp3 disk)
4- set db admin password using aws secrets manager
Create a A Multi-AZ DB cluster with one primary DB instance and one readable standby DB instances.





steps

1- create db with pass
2- create backend ags
3- create backend codebuild 
3- confiugre composer & nginx 
4- deploy backend & configure with db access
5- test backend api 

6- create frontend ags
7- create frontend codebuild
8- deploy frontend
9- test frontend

10- create code pipeline