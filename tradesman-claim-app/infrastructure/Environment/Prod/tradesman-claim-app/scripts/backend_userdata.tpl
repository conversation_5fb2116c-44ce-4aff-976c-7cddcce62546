#!/bin/bash

# Update the package repository and install necessary packages
sudo apt-get update -y
sudo apt-get install build-essential libssl-dev curl unzip zip -y
sudo apt-get install php-fpm php-mysql php-cli php-zip php-xml php-mbstring -y

sudo add-apt-repository ppa:ondrej/php -y
sudo apt-get update -y
sudo apt install php-curl -y
sudo apt-get update -y

# Remove Apache if installed
if dpkg -l | grep -q apache2; then
    echo "Removing Apache2..."
    sudo systemctl stop apache2
    sudo apt-get remove apache2 -y
    sudo apt purge apache2 -y 
    sudo rm -rf /usr/sbin/apache2
    sudo rm -rf /etc/apache2/
fi

sudo apt install nginx -y
# Enable and start PHP-FPM service
sudo systemctl enable php8.3-fpm.service
sudo systemctl start php8.3-fpm.service
sudo systemctl enable nginx
sudo systemctl start nginx
cat << 'EOF' | sudo tee /etc/nginx/sites-available/tradesman-backend.conf  
server {
    listen 80;
    server_name _;
    root /var/www/tradesman-claim-app-backend/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    location /health {
        access_log off;
        return 200 'OK';
        add_header Content-Type text/plain;
    }

    location /api/health {
        access_log off;
        return 200 'OK';
        add_header Content-Type text/plain;
    }
    
    
    location ~* (phpunit|eval-stdin\.php) {
        deny all;
        access_log /var/log/nginx/exploit.log;
        return 444;  # Close connection silently
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        include snippets/fastcgi-php.conf;

        fastcgi_pass unix:/run/php/php8.3-fpm.sock;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
EOF

# Enable the tradesman-app configuration
sudo ln -s /etc/nginx/sites-available/tradesman-backend.conf /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default
# Test Nginx configuration
sudo nginx -t

# Restart Nginx to apply changes
sudo systemctl restart nginx

# Verify the installed PHP version
php -v

sudo /etc/php/8.3/fpm/pool.d/www.conf /etc/php/8.3/fpm/pool.d/www.conf.bkup

cat << 'EOF' | sudo tee /etc/php/8.3/fpm/pool.d/www.conf
[www]
user = www-data
group = www-data
listen = /run/php/php8.3-fpm.sock
listen.owner = www-data
listen.group = www-data
;listen.mode = 0660

; Choose how the process manager will control the number of child processes.
; Possible Values:
;   static  - a fixed number (pm.max_children) of child processes;
;   dynamic - the number of child processes are set dynamically based on the
;             following directives. With this process management, there will be
;             always at least 1 children.
;             pm.max_children      - the maximum number of children that can
;                                    be alive at the same time.
;             pm.start_servers     - the number of children created on startup.
;             pm.min_spare_servers - the minimum number of children in 'idle'
;                                    state (waiting to process). If the number
;                                    of 'idle' processes is less than this
;                                    number then some children will be created.
;             pm.max_spare_servers - the maximum number of children in 'idle'
;                                    state (waiting to process). If the number
;                                    of 'idle' processes is greater than this
;                                    number then some children will be killed.
;             pm.max_spawn_rate    - the maximum number of rate to spawn child
;                                    processes at once.
;  ondemand - no children are created at startup. Children will be forked when
;             new requests will connect. The following parameter are used:
;             pm.max_children           - the maximum number of children that
;                                         can be alive at the same time.
;             pm.process_idle_timeout   - The number of seconds after which
;                                         an idle process will be killed.
; Note: This value is mandatory.
pm = dynamic

pm.max_children = 50

pm.start_servers = 10

pm.min_spare_servers = 5

pm.max_spare_servers = 20

; The number of rate to spawn child processes at once.
; Note: Used only when pm is set to 'dynamic'
; Note: Mandatory when pm is set to 'dynamic'
; Default Value: 32
;pm.max_spawn_rate = 32

; The number of seconds after which an idle process will be killed.
; Note: Used only when pm is set to 'ondemand'
; Default Value: 10s
;pm.process_idle_timeout = 10s;

; The number of requests each child process should execute before respawning.
; This can be useful to work around memory leaks in 3rd party libraries. For
; endless request processing specify '0'. Equivalent to PHP_FCGI_MAX_REQUESTS.
; Default Value: 0
pm.max_requests = 500

EOF

sudo systemctl restart php8.3-fpm


sudo apt install npm -y

# Create the destination folder for NVM under /usr/local/nvm
# mkdir -p /usr/local/nvm

# # Install NVM (Node Version Manager) - Use a more recent version of NVM
# curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | NVM_DIR=/usr/local/nvm bash

# # Create the nvm.sh script in /etc/profile.d to make NVM available globally
# cat << 'EOF' | sudo tee /etc/profile.d/nvm.sh
# #!/usr/bin/env bash
# export NVM_DIR="/usr/local/nvm"
# [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
# [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
# EOF

# # Set permissions for nvm.sh
# sudo chmod 755 /etc/profile.d/nvm.sh

# # Source NVM to make it available in the current session
# source /etc/profile.d/nvm.sh

# # Install Node.js using NVM
# nvm install node

# # Make NVM and Node.js available to the ubuntu user as well
# sudo -i -u ubuntu bash << EOF
#     export NVM_DIR="/usr/local/nvm"
#     [ -s "\$NVM_DIR/nvm.sh" ] && \. "\$NVM_DIR/nvm.sh"
#     nvm install node
#     nvm use node
# EOF

# Install Composer (PHP Dependency Manager) with checksum verification
export COMPOSER_HOME="/home/<USER>/.config/composer"
EXPECTED_CHECKSUM="$(php -r 'copy("https://composer.github.io/installer.sig", "php://stdout");')"
php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
ACTUAL_CHECKSUM="$(php -r "echo hash_file('sha384', 'composer-setup.php');")"

if [ "$EXPECTED_CHECKSUM" != "$ACTUAL_CHECKSUM" ]; then
    >&2 echo 'ERROR: Invalid installer checksum'
    rm composer-setup.php
    exit 1
fi

php composer-setup.php --quiet
RESULT=$?
rm composer-setup.php
mv composer.phar /usr/local/bin/composer

# Verify installation versions of PHP, Node.js, and Composer
php -v
node -v
composer -v

# install aws codedeploy agent cli ubuntu
sudo apt install ruby wget -y

# Install the CodeDeploy agent
sudo mkdir -p /home/<USER>/codedeploy
cd /home/<USER>/codedeploy
sudo wget https://aws-codedeploy-us-east-1.s3.us-east-1.amazonaws.com/latest/install
sudo chmod +x ./install
sudo ./install auto

# Start the CodeDeploy agent
sudo service codedeploy-agent start

# Enable the CodeDeploy agent to start on boot
sudo systemctl enable codedeploy-agent

# Check the status of the CodeDeploy agent
sudo service codedeploy-agent status
