#!/bin/bash

# Enable strict error handling
set -euo pipefail

# Logging function
log() {
  local level="$1"
  local message="$2"
  local timestamp
  timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] [$level] $message" | tee -a "/home/<USER>/scripts/script_execution.log"
}

# Variables
timestamp=$(date +"%Y-%m-%d_%H-%M-%S")
OUTPUT_DIR="/home/<USER>/scripts/exported_csv_files"
CSV_FILE="${OUTPUT_DIR}/exported_data_$timestamp.csv"
TARGET_MYSQL_DATABASE="claim_app"
TARGET_MYSQL_STAGE_TABLE="claim_form_data_staging"
TARGET_MYSQL_FINAL_TABLE="claim_form_data"
MYSQL_LOGIN_PATH="snowflake_tradesman"
LOG_FILE="/home/<USER>/scripts/script_execution.log"

# Start logging to file
log "INFO" "Starting script execution..."

# Create output directory if not exists
log "INFO" "Checking and creating output directory..."
mkdir -p "$OUTPUT_DIR"
log "INFO" "Output directory is set to $OUTPUT_DIR."

# Export query result to CSV using SnowSQL
log "INFO" "Exporting data from Snowflake..."
/home/<USER>/bin/snowsql -c claimsdb -q "SELECT * FROM PRODUCTION.ANALYTICS_REVERSE_ETL.REVERSE_ETL_CLAIM_APP_DATA_FEED;" \
        -o output_format=csv \
        -o header=false \
        -o friendly=false \
        -o timing=false \
        -o output_file="$CSV_FILE"
        
log "INFO" "Data exported successfully to $CSV_FILE."

# Fetch MySQL connection details from login path
MYSQL_HOST=$(mysql_config_editor print --login-path=$MYSQL_LOGIN_PATH | grep host | awk '{print $2}')
log "INFO" "Using MySQL host: $MYSQL_HOST"

# Connect to MySQL and truncate the staging table
log "INFO" "Connecting to MySQL to truncate the staging table: $TARGET_MYSQL_STAGE_TABLE..."
mysql --login-path="$MYSQL_LOGIN_PATH" \
      -h "$MYSQL_HOST" \
      -D "$TARGET_MYSQL_DATABASE" \
      -e "TRUNCATE TABLE $TARGET_MYSQL_STAGE_TABLE;"
log "INFO" "Table $TARGET_MYSQL_STAGE_TABLE truncated successfully."

# Load CSV into MySQL staging table using LOAD DATA LOCAL INFILE
log "INFO" "Loading data from $CSV_FILE into MySQL table $TARGET_MYSQL_STAGE_TABLE..."
mysql --login-path="$MYSQL_LOGIN_PATH" \
      -h "$MYSQL_HOST" \
      --local-infile=1 \
      -D "$TARGET_MYSQL_DATABASE" \
      -e "LOAD DATA LOCAL INFILE '$(realpath "$CSV_FILE")' 
          INTO TABLE $TARGET_MYSQL_STAGE_TABLE 
          FIELDS TERMINATED BY ',' 
          OPTIONALLY ENCLOSED BY '\"' 
          LINES TERMINATED BY '\n';"
log "INFO" "Data loaded successfully into MySQL table $TARGET_MYSQL_STAGE_TABLE."

# Insert new rows into the final table from the staging table
log "INFO" "Inserting new rows from staging table $TARGET_MYSQL_STAGE_TABLE into final table $TARGET_MYSQL_FINAL_TABLE..."
mysql --login-path="$MYSQL_LOGIN_PATH" \
      -h "$MYSQL_HOST" \
      -D "$TARGET_MYSQL_DATABASE" \
      -e "
        INSERT INTO $TARGET_MYSQL_FINAL_TABLE
        SELECT s.*
        FROM $TARGET_MYSQL_STAGE_TABLE s
        LEFT JOIN $TARGET_MYSQL_FINAL_TABLE f ON s.claim_number = f.claim_number
        WHERE f.claim_number IS NULL;"
log "INFO" "New rows successfully inserted into final table $TARGET_MYSQL_FINAL_TABLE."

# End of script
log "INFO" "Script execution completed successfully."