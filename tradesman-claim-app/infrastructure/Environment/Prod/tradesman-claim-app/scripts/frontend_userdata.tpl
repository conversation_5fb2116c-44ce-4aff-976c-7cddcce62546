#!/bin/bash
set -e

INSTANCE_ID=$(curl -s http://169.254.169.254/latest/meta-data/instance-id)


# Update package list
sudo apt update -y

cd /home/<USER>

# Install NGINX
sudo apt install nginx -y

# Enable and start NGINX service
sudo systemctl enable nginx
sudo systemctl start nginx


# Create the Nginx configuration for tradesman-app
cat << 'EOF' | sudo tee /etc/nginx/conf.d/limit_req.conf
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
EOF

cat << 'EOF' | sudo tee /etc/nginx/conf.d/backend.conf
    resolver 10.0.0.2 valid=30s;  # Use your VPC DNS server
    resolver_timeout 5s;
     upstream backend {
        server internal-prod-claim-app-backend-alb-964241237.us-east-1.elb.amazonaws.com;
    }
EOF

#Create logging conf
cat << 'EOF' | sudo tee /etc/nginx/conf.d/logging.conf
    # Enhanced log format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    log_format api_detailed '$remote_addr - $remote_user [$time_local] '
                           '"$request" $status $body_bytes_sent '
                           '"$http_referer" "$http_user_agent" '
                           'rt=$request_time uct="$upstream_connect_time" '
                           'uht="$upstream_header_time" urt="$upstream_response_time" '
                           'upstream="$upstream_addr" cache="$upstream_cache_status"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;
EOF

cat << 'EOF' | sudo tee /etc/nginx/sites-available/tradesman-app.conf
server {
    listen 80;  # Change to listen 443 if using HTTPS
    server_name _;  # Replace with your domain name or server IP

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;

        # Optional: Additional headers
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /health {
        access_log off;
        return 200 'OK';
        add_header Content-Type text/plain;
    }

    location /api {
        access_log /var/log/nginx/api.log api_detailed;
        
        # Add these for better debugging
        proxy_set_header X-Request-ID $request_id;
        add_header X-Request-ID $request_id;

        #limit_req zone=api_limit burst=20;
        proxy_pass http://internal-prod-claim-app-backend-alb-964241237.us-east-1.elb.amazonaws.com;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;

        # Preserve client IP and protocol information
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_hide_header X-Powered-By;

        # Allow WebSocket connections
        # proxy_set_header X-Forwarded-Host $host;
        
        # Timeout settings
        proxy_connect_timeout 15s;
        proxy_send_timeout    10s;
        proxy_read_timeout    60s;  # Increase if APIs are slow

        # Buffering tweaks
        proxy_buffering on;
        proxy_buffer_size 16k; # Larger buffer for API JSON responses
        proxy_buffers 4 32k; # Fewer, larger buffers for API traffic
        proxy_busy_buffers_size 64k;

    }

    location /health-app {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;

        # Optional: Additional headers
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location ~* (phpunit|eval-stdin\.php) {
        deny all;
        access_log /var/log/nginx/exploit.log;
        return 444;  # Close connection silently
    }

    # error_page 500 502 503 504 /50x.html;  # Custom error pages
    # location = /50x.html {
    #     root /usr/share/nginx/html;  # Default error page location
    # }
}
EOF




# Enable the tradesman-app configuration
sudo ln -s /etc/nginx/sites-available/tradesman-app.conf /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default

# Test Nginx configuration
sudo nginx -t

# Restart Nginx to apply changes
sudo systemctl restart nginx

# Create the destination folder for NVM under /usr/local/nvm
sudo mkdir -p /usr/local/nvm

sudo apt install npm -y
sudo npm install -g pm2 -y


# install aws codedeploy agent cli ubuntu
sudo apt install ruby wget -y


# Download and install CloudWatch agent for Ubuntu
wget https://s3.amazonaws.com/amazoncloudwatch-agent/ubuntu/amd64/latest/amazon-cloudwatch-agent.deb
sudo dpkg -i ./amazon-cloudwatch-agent.deb

# If there are dependency issues, fix them
sudo apt-get update
sudo apt-get install -f

sudo tee /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json << 'EOF'
{
  "logs": {
    "logs_collected": {
      "files": {
        "collect_list": [
          {
            "file_path": "/var/log/nginx/access.log",
            "log_group_name": "/aws/ec2/nginx/access",
            "log_stream_name": "$INSTANCE_ID",
            "timezone": "UTC"
          },
          {
            "file_path": "/var/log/nginx/error.log",
            "log_group_name": "/aws/ec2/nginx/error",
            "log_stream_name": "$INSTANCE_ID",
            "timezone": "UTC"
          },
          {
            "file_path": "/var/log/nginx/api.log",
            "log_group_name": "/aws/ec2/nginx/api",
            "log_stream_name": "$INSTANCE_ID",
            "timezone": "UTC"
          },
          {
            "file_path": "/var/log/fail2ban.log",
            "log_group_name": "/aws/ec2/fail2ban",
            "log_stream_name": "$INSTANCE_ID",
            "timezone": "UTC"
          }
        ]
      }
    }
  },
  "metrics": {
    "namespace": "CWAgent",
    "metrics_collected": {
      "cpu": {
        "measurement": [
          "cpu_usage_idle",
          "cpu_usage_iowait",
          "cpu_usage_user",
          "cpu_usage_system"
        ],
        "metrics_collection_interval": 60
      },
      "disk": {
        "measurement": [
          "used_percent"
        ],
        "metrics_collection_interval": 60,
        "resources": [
          "*"
        ]
      },
      "diskio": {
        "measurement": [
          "io_time"
        ],
        "metrics_collection_interval": 60,
        "resources": [
          "*"
        ]
      },
      "mem": {
        "measurement": [
          "mem_used_percent"
        ],
        "metrics_collection_interval": 60
      },
      "netstat": {
        "measurement": [
          "tcp_established",
          "tcp_time_wait"
        ],
        "metrics_collection_interval": 60
      }
    }
  }
}
EOF

# Start the agent
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl \
  -a fetch-config -m ec2 -s \
  -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json

# Enable auto-start
sudo systemctl enable amazon-cloudwatch-agent
sudo systemctl start amazon-cloudwatch-agent

# Install the CodeDeploy agent
sudo mkdir -p /home/<USER>/codedeploy
cd /home/<USER>/codedeploy
sudo wget https://aws-codedeploy-us-east-1.s3.us-east-1.amazonaws.com/latest/install
sudo chmod +x ./install
sudo ./install auto

# Start the CodeDeploy agent
sudo service codedeploy-agent start

# Enable the CodeDeploy agent to start on boot
sudo systemctl enable codedeploy-agent

# Check the status of the CodeDeploy agent
sudo service codedeploy-agent status

sudo systemctl restart nginx

sudo apt install fail2ban -y

cat << 'EOF' | sudo tee /etc/fail2ban/jail.d/nginx.conf
[nginx-http-auth]
enabled  = true
port     = http,https
filter   = nginx-http-auth
logpath  = /var/log/nginx/error.log
findtime = 10m
maxretry = 5
bantime  = 24h
ignoreip = 127.0.0.1/8 ::1

[nginx-badbots]
enabled  = true
port     = http,https
filter   = nginx-badbots
logpath  = /var/log/nginx/access.log
maxretry = 2
bantime  = 48h
EOF

sudo systemctl restart fail2ban


cat << 'EOF' | sudo tee /home/<USER>/nginx-api-monitor.sh
#!/bin/bash
TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")
while true; do
    if curl -f -s http://localhost/api/health > /dev/null; then
        echo " [$TIMESTAMP] Backend healthy"
    else
        echo " [$TIMESTAMP] Backend unhealthy, restarting nginx"
        sudo systemctl restart nginx
        sleep 30
    fi
    sleep 60
done
EOF

sudo chmod +x /home/<USER>/nginx-api-monitor.sh

#create crontab for ubuntu
sudo crontab -u ubuntu -e && echo "1"

echo "*/5 * * * * /home/<USER>/nginx-api-monitor.sh" | sudo tee /etc/cron.d/nginx-api-monitor

