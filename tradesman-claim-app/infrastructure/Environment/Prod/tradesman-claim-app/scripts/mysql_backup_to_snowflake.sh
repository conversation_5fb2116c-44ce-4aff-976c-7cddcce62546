#!/bin/bash

set -euo pipefail

# Logging function
log() {
  local level="$1"
  local message="$2"
  local timestamp
  timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] [$level] $message" | tee -a "./mysql_export.log"
}

# Variables
MYSQL_LOGIN_PATH="snowflake_tradesman"
MYSQL_DATABASE="claimappdb"
MYSQL_TABLE="claim_form_data"
OUTPUT_DIR="./mysql_csv_exports"
LOG_FILE="./mysql_csv_export.log"
S3_BUCKET_KEY="claimapp-backup/mysql_export"
BATCH_SIZE=1000
TIMESTAMP=$(date +"%Y-%m-%d_%H-%M-%S")
FINAL_CSV_FILE="${OUTPUT_DIR}/claim_form_data_export_${TIMESTAMP}.csv"

# Create output directory if not exists
log "INFO" "Checking and creating output directory..."
mkdir -p "$OUTPUT_DIR"
log "INFO" "Output directory is set to $OUTPUT_DIR."

# Get total row count
ROW_COUNT=$(mysql --login-path="$MYSQL_LOGIN_PATH" -D "$MYSQL_DATABASE" -N -e "SELECT COUNT(*) FROM $MYSQL_TABLE;")
log "INFO" "Total rows in $MYSQL_TABLE: $ROW_COUNT"

# Export in batches and append to single CSV
OFFSET=0
BATCH_NUM=1
HEADER_WRITTEN=0
while [ $OFFSET -lt $ROW_COUNT ]; do
  log "INFO" "Exporting batch $BATCH_NUM: rows $OFFSET to $((OFFSET+BATCH_SIZE-1))..."
  if [ $HEADER_WRITTEN -eq 0 ]; then
    # Write header and data for first batch
    mysql --login-path="$MYSQL_LOGIN_PATH" -D "$MYSQL_DATABASE" -B -e \
      "SELECT * FROM $MYSQL_TABLE LIMIT $BATCH_SIZE OFFSET $OFFSET;" \
      | awk 'BEGIN{FS="\t"; OFS=","} NR==1{for(i=1;i<=NF;i++) $i="\"" $i "\""; print; next} {for(i=1;i<=NF;i++) {gsub(/\"/, "\"\"", $i); $i="\"" $i "\""} print}' > "$FINAL_CSV_FILE"
    HEADER_WRITTEN=1
  else
    # Append data only (skip header)
    mysql --login-path="$MYSQL_LOGIN_PATH" -D "$MYSQL_DATABASE" -B -e \
      "SELECT * FROM $MYSQL_TABLE LIMIT $BATCH_SIZE OFFSET $OFFSET;" \
      | tail -n +2 \
      | awk 'BEGIN{FS="\t"; OFS=","} {for(i=1;i<=NF;i++) {gsub(/\"/, "\"\"", $i); $i="\"" $i "\""} print}' >> "$FINAL_CSV_FILE"
  fi
  log "INFO" "Batch $BATCH_NUM exported and appended to $FINAL_CSV_FILE."
  OFFSET=$((OFFSET+BATCH_SIZE))
  BATCH_NUM=$((BATCH_NUM+1))
done

log "INFO" "All batches exported to $FINAL_CSV_FILE. Check $LOG_FILE for details."

# Upload to S3
S3_PATH="s3://${S3_BUCKET_KEY}/$(basename "$FINAL_CSV_FILE")"
log "INFO" "Uploading $FINAL_CSV_FILE to $S3_PATH ..."
if aws s3 cp "$FINAL_CSV_FILE" "$S3_PATH"; then
  log "INFO" "Upload successful. Deleting local file $FINAL_CSV_FILE ..."
  rm -f "$FINAL_CSV_FILE"
  log "INFO" "Local file deleted. Final S3 file location: $S3_PATH"
else
  log "ERROR" "Failed to upload $FINAL_CSV_FILE to S3. Local file retained."
fi