#!/bin/bash

# Update the package repository and install necessary packages
sudo apt-get update -y
sudo apt-get install build-essential libssl-dev curl unzip zip -y

# Install AWS CLI for secrets management
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install


# Install SnowSQL (Snowflake CLI)
echo "Installing SnowSQL..."
cd /tmp
curl -O https://sfc-repo.snowflakecomputing.com/snowsql/bootstrap/1.3/linux_x86_64/snowsql-1.3.3-linux_x86_64.bash
chmod +x snowsql-1.3.3-linux_x86_64.bash
sudo mkdir -p /home/<USER>/bin
sudo chown ubuntu:ubuntu /home/<USER>/bin
sudo -u ubuntu SNOWSQL_DEST=/home/<USER>/bin SNOWSQL_LOGIN_SHELL=/home/<USER>/.profile bash /tmp/snowsql-1.3.3-linux_x86_64.bash

# Create directory for Snowflake configuration
sudo mkdir -p /home/<USER>/.snowflake
sudo chmod 700 /home/<USER>/.snowflake
sudo chown ubuntu:ubuntu /home/<USER>/.snowflake

# Set up Snowflake credentials from AWS Secrets Manager
cat << 'EOF' | sudo tee /home/<USER>/fetch_snowflake_credentials.sh
#!/bin/bash
# Fetch Snowflake credentials from AWS Secrets Manager
SNOWFLAKE_CREDS=$(aws secretsmanager get-secret-value --secret-id tradesman-snowflake-credentials --query SecretString --output text --region us-east-1)

# Extract credentials
SF_ACCOUNT=$(echo $SNOWFLAKE_CREDS | jq -r '.account')
SF_USER=$(echo $SNOWFLAKE_CREDS | jq -r '.username')
SF_PASSWORD=$(echo $SNOWFLAKE_CREDS | jq -r '.password')
SF_DATABASE=$(echo $SNOWFLAKE_CREDS | jq -r '.database')
SF_ROLE=$(echo $SNOWFLAKE_CREDS | jq -r '.rolename')
SF_WAREHOUSE=$(echo $SNOWFLAKE_CREDS | jq -r '.warehouse')

# Create SnowSQL config file
cat > /home/<USER>/.snowsql/config << SNOWCFG
[connections]
accountname = $SF_ACCOUNT
username = $SF_USER
password = $SF_PASSWORD
dbname = $SF_DATABASE
rolename = $SF_ROLE
warehousename = $SF_WAREHOUSE

[connections.my_connection]
accountname = $SF_ACCOUNT
username = $SF_USER
password = $SF_PASSWORD
dbname = $SF_DATABASE
rolename = $SF_ROLE
warehousename = $SF_WAREHOUSE
SNOWCFG

# Set proper permissions
chmod 600 /home/<USER>/.snowsql/config
chown ubuntu:ubuntu /home/<USER>/.snowsql/config

EOF

sudo chmod +x /home/<USER>/fetch_snowflake_credentials.sh
sudo chown ubuntu:ubuntu /home/<USER>/fetch_snowflake_credentials.sh

# Run the script to fetch credentials
sudo -u ubuntu /home/<USER>/fetch_snowflake_credentials.sh

# Add SnowSQL to PATH for all users
echo 'export PATH="/home/<USER>/bin:$PATH"' | sudo tee /etc/profile.d/snowsql.sh
sudo chmod +x /etc/profile.d/snowsql.sh

# Install MySQL client
sudo apt-get install mysql-client -y

cd /home/<USER>
sudo mkdir -p /home/<USER>/scripts
sudo chown ubuntu:ubuntu /home/<USER>/scripts
cd /home/<USER>/scripts

# Create the Snowflake export/import script
cat << 'EOF' | sudo tee /home/<USER>/scripts/snowflake_mysql_export_import.sh

EOF

sudo chmod +x /home/<USER>/scripts/snowflake_mysql_export_import.sh
sudo chown ubuntu:ubuntu /home/<USER>/scripts/snowflake_mysql_export_import.sh

# Create log directory
sudo mkdir -p /home/<USER>/scripts/exported_csv_files
sudo chown -R ubuntu:ubuntu /home/<USER>/scripts/

# Set up MySQL login credentials for the cron job
cat << 'EOF' | sudo tee /home/<USER>/setup_mysql_login.sh
#!/bin/bash
# Fetch MySQL credentials from AWS Secrets Manager
MYSQL_CREDS=$(aws secretsmanager get-secret-value --secret-id tradesman-mysql-credentials --query SecretString --output text --region us-east-1)

# Extract credentials
MYSQL_HOST=$(echo $MYSQL_CREDS | jq -r '.host')
MYSQL_USER=$(echo $MYSQL_CREDS | jq -r '.username')
MYSQL_PASS=$(echo $MYSQL_CREDS | jq -r '.password')
MYSQL_DB=$(echo $MYSQL_CREDS | jq -r '.dbname')

# Create MySQL login path
mysql_config_editor set --login-path=snowflake_tradesman --host="$MYSQL_HOST" --user="$MYSQL_USER" --password

# Test the connection
echo "Testing MySQL connection..."
if mysql --login-path=snowflake_tradesman -e "SELECT 1;" > /dev/null 2>&1; then
    echo "MySQL connection successful!"
else
    echo "MySQL connection failed. Please check credentials and network connectivity."
    exit 1
fi

# Store the database name in a config file for the script to use
echo "MYSQL_DATABASE=$MYSQL_DB" > /home/<USER>/scripts/mysql_config
chmod 600 /home/<USER>/scripts/mysql_config
EOF

sudo chmod +x /home/<USER>/setup_mysql_login.sh
sudo chown ubuntu:ubuntu /home/<USER>/setup_mysql_login.sh
sudo -u ubuntu /home/<USER>/setup_mysql_login.sh

# Create a cron job to run the script every hour
cat << 'EOF' | sudo tee /etc/cron.d/snowflake_sync
# Run Snowflake to MySQL sync every hour
0 0 * * * ubuntu /home/<USER>/scripts/snowflake_mysql_export_import.sh >> /home/<USER>/scripts/cron_execution.log 2>&1
EOF

sudo chmod 644 /etc/cron.d/snowflake_sync

# Restart cron service to apply changes
sudo systemctl restart cron
