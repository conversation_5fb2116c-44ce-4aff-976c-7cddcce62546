# Load user-data template and pass variables dynamically
data "template_file" "backend_user_data" {
  template = file("${path.module}/scripts/backend_userdata.tpl")
}
data "template_file" "frontend_user_data" {
  template = file("${path.module}/scripts/frontend_userdata.tpl")
}

data "template_file" "snowflake_user_data" {
  template = file("${path.module}/scripts/snowflake_userdata.tpl")
}

module "vpc" {
  source      = "../../../modules/tradesman-aws-vpc"
  name        = "Tradesman"
  environment = "prod"
  no_of_azs   = 3
  tags = {
    "Environment" = "Production"
    "Department"  = "DevOps"
    "Project"     = "Tradesman"
  }
}
module "sg" {
  source = "../../../modules/tradesman-sg"
  security_groups = [
    {
      name        = "prod-claim-app-frontend-sg"
      description = "Security group for claim app frontend servers"
      vpc_id      = module.vpc.id
      tags = {
        Name        = "prod-claim-app-frontend-sg"
        Environment = "Production"
      }
    },
    {
      name        = "prod-claim-app-db-sg"
      description = "Security group for claim app db servers"
      vpc_id      = module.vpc.id
      tags = {
        Name        = "prod-claim-app-db-sg"
        Environment = "Production"
      }
    },
    {
      name        = "prod-claim-app-backend-sg"
      description = "Security group for claim app backend servers"
      vpc_id      = module.vpc.id
      tags = {
        Name        = "prod-claim-app-backend-sg"
        Environment = "Production"
      }
    }

  ]
}
module "sg-rules-1" {
  source            = "../../../modules/tradesman-sg-rules"
  security_group_id = lookup(module.sg.security_group_ids, "prod-claim-app-frontend-sg")
  ingress_rules = [
    {
      cidr_ipv4 = ["0.0.0.0/0"] # List of CIDR blocks
      from_port = 22
      to_port   = 22
      protocol  = "tcp"
    },
    {
      cidr_ipv4 = ["0.0.0.0/0"] # List of CIDR blocks
      from_port = 80
      to_port   = 80
      protocol  = "tcp"
    },
    {
      cidr_ipv4 = ["0.0.0.0/0"] # List of CIDR blocks
      from_port = 443
      to_port   = 443
      protocol  = "tcp"
    }
  ]
}
module "sg-rules-2" {
  source            = "../../../modules/tradesman-sg-rules"
  security_group_id = lookup(module.sg.security_group_ids, "prod-claim-app-db-sg")
  ingress_rules_reference = [
    {
      reference_security = [lookup(module.sg.security_group_ids, "prod-claim-app-backend-sg"), lookup(module.sg.security_group_ids, "prod-claim-app-frontend-sg")] # Example security group reference
      from_port          = 3306
      to_port            = 3306
      protocol           = "tcp"
    }
  ]
}
module "sg-rules-3" {
  source            = "../../../modules/tradesman-sg-rules"
  security_group_id = lookup(module.sg.security_group_ids, "prod-claim-app-backend-sg")
  ingress_rules = [
    {
      cidr_ipv4 = [module.vpc.cidr] # Using the correct output attribute name
      from_port = 80
      to_port   = 80
      protocol  = "tcp"
    }
  ]
  ingress_rules_reference = [
    {
      reference_security = [lookup(module.sg.security_group_ids, "prod-claim-app-frontend-sg")]
      from_port          = 22
      to_port            = 22
      protocol           = "tcp"
    },
    {
      reference_security = [lookup(module.sg.security_group_ids, "prod-claim-app-frontend-sg")]
      from_port          = 80
      to_port            = 80
      protocol           = "tcp"
    },
    {
      reference_security = [lookup(module.sg.security_group_ids, "prod-claim-app-frontend-sg")]
      from_port          = 443
      to_port            = 443
      protocol           = "tcp"
    }
  ]
}

# Add explicit rule to allow traffic from anywhere in the VPC to the backend SG
/*
module "sg-rules-backend-alb" {
  source            = "../../../modules/tradesman-sg-rules"
  security_group_id = lookup(module.sg.security_group_ids, "prod-claim-app-backend-sg")
  
  # Allow HTTP traffic from anywhere in the VPC
  ingress_rules = [
    {
      cidr_ipv4 = [module.vpc.cidr]
      from_port = 80
      to_port   = 80
      protocol  = "tcp"
    },
    {
      cidr_ipv4 = [module.vpc.cidr]
      from_port = 443
      to_port   = 443
      protocol  = "tcp"
    }
  ]
  
  # Use the egress_ports variable instead of egress_rules
  egress_ports = [
    {
      protocol  = "-1"
      from_port = 0
      to_port   = 0
    }
  ]
  
  # Set CIDR blocks for egress
  cidr_blocks = ["0.0.0.0/0"]
}
*/



#RDS 

# Create a secret for the database password
resource "aws_secretsmanager_secret" "db_password" {
  name        = "prod-tradesman-claim-app-db-password"
  description = "Password for the RDS database"
  tags = {
    Environment = "Production"
    Project     = "Tradesman"
  }
}

resource "aws_secretsmanager_secret_version" "db_password" {
  secret_id     = aws_secretsmanager_secret.db_password.id
  secret_string = jsonencode({
    username = "admin"
    password = random_password.rds_password.result # Replace with a secure password or use random generation
  })
}

resource "random_password" "rds_password" {
  length           = 16
  special          = true
  override_special = "!#$%&*()-_=+[]{}<>:?"
}


# KMS key for RDS encryption
resource "aws_kms_key" "rds_encryption_key" {
  description             = "KMS key for RDS encryption"
  deletion_window_in_days = 10
  enable_key_rotation     = true
  
  tags = {
    Name        = "prod-tradesman-claim-app-db-kms-key"
    Environment = "Production"
    Project     = "Tradesman"
  }
}

resource "aws_kms_alias" "rds_encryption_key_alias" {
  name          = "alias/prod-tradesman-claim-app-db-key"
  target_key_id = aws_kms_key.rds_encryption_key.key_id
}

# Production Multi-AZ RDS Instance (not cluster)
module "mysql_rds_db" {
  source                  = "../../../modules/tradesman-aws-rds"
  identifier              = "prod-tradesman-claim-app-db"
  engine                  = "mysql"
  engine_version          = "8.0"
  family                  = "mysql8.0"
  major_engine_version    = "8.0"
  instance_class          = "db.m7g.large"
  
  # Multi-AZ configuration - standard Multi-AZ deployment (not cluster)
  multi_az               = true
  availability_zone      = null  # Let AWS choose the AZ for primary
  
  # Explicitly disable cluster mode
  is_db_cluster          = false
  
  # Storage configuration
  allocated_storage      = 10  
  
  # Database details
  db_name                = "claimappdb"
  username               = "admin"
  manage_master_user_password = true
  master_user_secret_kms_key_id = aws_kms_key.rds_encryption_key.key_id
  iam_database_authentication_enabled= true
  
  # Network configuration
  port                   = 3306
  subnet_ids             = module.vpc.private_subnets
  vpc_security_group_ids = [lookup(module.sg.security_group_ids, "prod-claim-app-db-sg")]
  
  # Backup configuration
  backup_retention_period = 7
  backup_window           = "03:00-05:00"
  maintenance_window      = "Mon:00:00-Mon:03:00"
  
  # Parameter and option groups
  create_db_subnet_group    = true
  create_db_parameter_group = true
  create_db_option_group    = false
  
  # Enhanced monitoring
  monitoring_interval       = 60
  monitoring_role_name      = "prod-tradesman-claim-app-db-monitoring-role"
  create_monitoring_role    = true
  
  # Performance insights
  performance_insights_enabled          = true
  performance_insights_retention_period = 7
  
  # Encryption
  storage_encrypted = true
  kms_key_id        = aws_kms_key.rds_encryption_key.arn
  
  # Tags
  tags = {
    Name        = "prod-tradesman-claim-app-db"
    Environment = "Production"
    Project     = "Tradesman"
  }
}

# # Load user-data template and pass variables dynamically

# module "mysql-dev" {
#   source                    = "../../../modules/tradesman-aws-rds"
#   identifier                = "prod-tradesman-claim-app-db"
#   engine                    = "mysql"
#   engine_version            = "8.0"
#   family                    = "mysql8.0"
#   major_engine_version      = "8.0"
#   instance_class            = "db.t3.small" # Changed from db.t3.micro to db.t3.small
#   create_db_subnet_group    = true
#   allocated_storage         = 20
#   db_name                   = "claimappdb"
#   username                  = "admin_claimappdb"
#   port                      = 3306
#   subnet_ids                = module.vpc.private_subnets
#   vpc_security_group_ids    = [lookup(module.sg.security_group_ids, "prod-claim-app-db-sg")]
#   backup_retention_period   = 1 # Changed from 0 to 1 to enable backups required for replicas
#   create_db_option_group    = false
#   create_db_parameter_group = true

#   # Add read replica configuration
#   replicate_source_db = null # This is the master instance
# }

# # Add read replica instance
# module "mysql-replica" {
#   source = "../../../modules/tradesman-aws-rds"

#   identifier = "prod-tradesman-claim-app-db-replica"

#   # Source database
#   replicate_source_db = module.mysql-dev.db_instance_identifier

#   # Required parameters for replica
#   engine               = "mysql"
#   engine_version       = "8.0"
#   family               = "mysql8.0"
#   major_engine_version = "8.0"
#   instance_class       = "db.m7g.large"

#   # Storage configuration
#   allocated_storage = 20

#   # Network configuration
#   port                   = 3306
#   vpc_security_group_ids = [lookup(module.sg.security_group_ids, "prod-claim-app-db-sg")]

#   # Skip these for replica
#   create_db_subnet_group    = false
#   create_db_option_group    = false
#   create_db_parameter_group = false

#   # Tags
#   tags = {
#     Name        = "prod-tradesman-claim-app-db-replica"
#     Environment = "Production"
#   }
# }

# Frontend ASG with ALB
module "frontend_asg" {
  source = "../../../modules/tradesman-aws-asg"

  name_prefix        = "prod-claim-app-frontend"
  image_id           = "ami-0e86e20dae9224db8"
  instance_type      = "t3.small"
  key_name           = aws_key_pair.generated_key.id
  security_group_ids = [lookup(module.sg.security_group_ids, "prod-claim-app-frontend-sg")]
  vpc_id             = module.vpc.id
  subnet_ids         = module.vpc.public_subnets
  user_data          = data.template_file.frontend_user_data.rendered
  volume_size        = 10
  desired_capacity   = 1
  min_size           = 1
  max_size           = 2
  health_check_type  = "EC2, ELB"
  
  # Add IAM role for CodeDeploy
  iam_instance_profile = aws_iam_instance_profile.ec2_codedeploy_profile.name

  # ALB configuration
  create_lb          = true
  lb_internal        = false
  lb_security_groups = [lookup(module.sg.security_group_ids, "prod-claim-app-frontend-sg")]

  tags = {
    Name        = "prod-claim-app-frontend"
    Environment = "Production"
  }
}

# Backend ASG in private subnet
module "backend_asg" {
  source = "../../../modules/tradesman-aws-asg"

  name_prefix        = "prod-claim-app-backend"
  image_id           = "ami-0e86e20dae9224db8"
  instance_type      = "t3.large"
  key_name           = aws_key_pair.generated_key.id
  security_group_ids = [lookup(module.sg.security_group_ids, "prod-claim-app-backend-sg")]
  vpc_id             = module.vpc.id
  subnet_ids         = module.vpc.private_subnets
  user_data          = data.template_file.backend_user_data.rendered
  volume_size        = 50
  desired_capacity   = 2
  min_size           = 1
  max_size           = 2

  health_check_type  = "ELB"
  iam_instance_profile = aws_iam_instance_profile.ec2_codedeploy_profile.name

  # ALB configuration for backend - FIXED
  create_lb          = true
  lb_internal        = true  # Changed to true for internal load balancer in private subnet
  lb_security_groups = [lookup(module.sg.security_group_ids, "prod-claim-app-backend-sg")]

  tags = {
    Name        = "prod-claim-app-backend"
    Environment = "Production"
  }
}

# Output the load balancer DNS names
output "frontend_lb_dns_name" {
  value       = module.frontend_asg.lb_dns_name
  description = "The DNS name of the frontend load balancer"
}

output "backend_lb_dns_name" {
  value       = module.backend_asg.lb_dns_name
  description = "The DNS name of the backend load balancer"
}

output "security_group_ids" {
  value       = module.sg.security_group_ids
  description = "Map of security group names to their IDs from the sg module."
}
output "private_subnets" {
  value       = module.vpc.*.private_subnets
  description = "Map of security group names to their IDs from the sg module."
}


output "rds_password" {
  value       = random_password.rds_password.result
  description = "The generated password for the RDS admin user"
  sensitive   = true
}

output "rds_secret_arn" {
  value       = aws_secretsmanager_secret.db_password.arn
  description = "The ARN of the Secrets Manager secret storing the RDS credentials"
}

# Create IAM role and instance profile for EC2 instances
resource "aws_iam_role" "ec2_codedeploy_role" {
  name = "Ec2CodeDeployRole-prod"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name        = "Ec2CodeDeployRole"
    Environment = "Production"
    Project     = "Tradesman"
  }
}

# Attach policies to the role
resource "aws_iam_role_policy_attachment" "codedeploy_agent_policy" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2RoleforAWSCodeDeploy"
  role       = aws_iam_role.ec2_codedeploy_role.name
}

resource "aws_iam_role_policy_attachment" "ssm_policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
  role       = aws_iam_role.ec2_codedeploy_role.name
}

# Create instance profile
resource "aws_iam_instance_profile" "ec2_codedeploy_profile" {
  name = "Ec2CodeDeployProfile-prod"
  role = aws_iam_role.ec2_codedeploy_role.name
}

# Snowflake EC2 Instance
resource "aws_instance" "snowflake_instance" {
  ami                    = "ami-0e86e20dae9224db8"  # Same AMI as other instances
  instance_type          = "t2.small"
  key_name               = aws_key_pair.generated_key.id
  subnet_id              = element(module.vpc.private_subnets, 0)
  vpc_security_group_ids = [lookup(module.sg.security_group_ids, "prod-claim-app-backend-sg")]
  iam_instance_profile   = aws_iam_instance_profile.snowflake_instance_profile.name
  user_data              = data.template_file.snowflake_user_data.rendered
  
  root_block_device {
    volume_type = "gp3"
    volume_size = 10
    encrypted   = true
  }
  
  tags = {
    Name        = "prod-claim-app-snowScripts"
    Environment = "Production"
    Project     = "Tradesman"
  }
}

# IAM role for Snowflake instance
resource "aws_iam_role" "snowflake_instance_role" {
  name = "prod-claim-app-snowflake-role"
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
  
  tags = {
    Name        = "prod-claim-app-snowflake-role"
    Environment = "Production"
    Project     = "Tradesman"
  }
}

# Attach policies to the role
resource "aws_iam_role_policy_attachment" "secrets_manager_policy" {
  policy_arn = "arn:aws:iam::aws:policy/SecretsManagerReadWrite"
  role       = aws_iam_role.snowflake_instance_role.name
}

resource "aws_iam_role_policy_attachment" "ssm_policy_snowflake" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
  role       = aws_iam_role.snowflake_instance_role.name
}

# Create instance profile
resource "aws_iam_instance_profile" "snowflake_instance_profile" {
  name = "prod-claim-app-snowflake-profile"
  role = aws_iam_role.snowflake_instance_role.name
}

# # Inline policy for specific Snowflake secret access
# resource "aws_iam_role_policy" "snowflake_secrets_policy" {
#   name   = "snowflake-secrets-access"
#   role   = aws_iam_role.snowflake_instance_role.name
  
#   policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Action = [
#           "secretsmanager:GetSecretValue",
#           "secretsmanager:DescribeSecret"
#         ]
#         Effect   = "Allow"
#         Resource = aws_secretsmanager_secret.snowflake_credentials.arn
#       },
#       {
#         Action = [
#           "secretsmanager:GetSecretValue",
#           "secretsmanager:DescribeSecret"
#         ]
#         Effect   = "Allow"
#         Resource = aws_secretsmanager_secret.mysql_credentials.arn
#       }
#     ]
#   })
# }

# Snowflake credentials secret
# resource "aws_secretsmanager_secret" "snowflake_credentials" {
#   name        = "tradesman-snowflake-credentials"
#   description = "Credentials for Snowflake database connection"
#   tags = {
#     Environment = "Production"
#     Project     = "Tradesman"
#   }
# }

# resource "aws_secretsmanager_secret_version" "snowflake_credentials" {
#   secret_id     = aws_secretsmanager_secret.snowflake_credentials.id
#   secret_string = jsonencode({
#     account   = "your-snowflake-account"     # Replace with actual value or use variable
#     username  = "your-snowflake-username"    # Replace with actual value or use variable
#     password  = "your-snowflake-password"    # Replace with actual value or use variable
#     database  = "your-snowflake-database"    # Replace with actual value or use variable
#     schema    = "your-snowflake-schema"      # Replace with actual value or use variable
#     warehouse = "your-snowflake-warehouse"   # Replace with actual value or use variable
#   })
# }

# # MySQL credentials secret for the Snowflake-to-MySQL sync
# resource "aws_secretsmanager_secret" "mysql_credentials" {
#   name        = "tradesman-mysql-credentials"
#   description = "Credentials for MySQL database connection"
#   tags = {
#     Environment = "Production"
#     Project     = "Tradesman"
#   }
# }



# # Output the Snowflake instance ID
output "snowflake_instance_id" {
  value       = aws_instance.snowflake_instance.id
  description = "The ID of the Snowflake EC2 instance"
}

output "snowflake_instance_private_ip" {
  value       = aws_instance.snowflake_instance.private_ip
  description = "The private IP of the Snowflake EC2 instance"
}

# output "snowflake_secret_arn" {
#   value       = aws_secretsmanager_secret.snowflake_credentials.arn
#   description = "The ARN of the Secrets Manager secret storing the Snowflake credentials"
# }

