# add rds read replica to the module mysql-dev  
# module "mysql-dev" {
#   source = "../../../modules/tradesman-aws-rds"
#   identifier = "prod-tradesman-claim-app-db"
#   engine               = "mysql"
#   engine_version       = "8.0"
#   family               = "mysql8.0"
#   major_engine_version = "8.0"
#   instance_class       = "db.t3.medium"
#   create_db_subnet_group = true
#   allocated_storage = 100
#   db_name  = "claimappdb"
#   username = "admin_claimappdb"
#   port     = 3306
#   subnet_ids             =  module.vpc.private_subnets
#   vpc_security_group_ids = [lookup(module.sg.security_group_ids, "prod-claim-app-db-sg")]
#   backup_retention_period = 0
#   create_db_option_group = false
#   create_db_parameter_group = true
#   read_replica = [
#     {
#       identifier = "prod-tradesman-claim-app-db-replica"
#       instance_class = "db.t3.medium"
#       allocated_storage = 100
#       availability_zone = "us-east-1a"
#       engine = "mysql"
#       engine_version = "8.0"
#       db_instance_class = "db.t3.micro"
#       db_subnet_group_name = module.mysql-dev.db_subnet_group_name
#       vpc_security_group_ids = [lookup(module.sg.security_group_ids, "prod-claim-app-db-sg")]
#       tags = {
#         Name = "prod-tradesman-claim-app-db-replica"
#         Environment = "Production"
#       }
#     }
#   ]
# }