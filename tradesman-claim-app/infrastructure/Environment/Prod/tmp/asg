# Create Launch Template for backend
resource "aws_launch_template" "backend" {
  name_prefix   = "prod-claim-app-backend-lt"
  image_id      = "ami-0e86e20dae9224db8"
  instance_type = "t2.small"

  network_interfaces {
    associate_public_ip_address = false
    security_groups            = [lookup(module.sg.security_group_ids, "prod-claim-app-backend-sg")]
  }

  block_device_mappings {
    device_name = "/dev/sda1"
    ebs {
      volume_size           = 50
      volume_type          = "gp2"
      delete_on_termination = true
    }
  }

  user_data = base64encode(data.template_file.backend_user_data.rendered)

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name        = "prod-claim-app-backend"
      Environment = "Production"
    }
  }
}

# Create Auto Scaling Group
resource "aws_autoscaling_group" "backend" {
  name                = "prod-claim-app-backend-asg"
  desired_capacity    = 2
  max_size           = 4
  min_size           = 1
  target_group_arns  = [aws_lb_target_group.backend.arn]
  vpc_zone_identifier = module.vpc.private_subnets

  launch_template {
    id      = aws_launch_template.backend.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = "prod-claim-app-backend"
    propagate_at_launch = true
  }

  tag {
    key                 = "Environment"
    value               = "Production"
    propagate_at_launch = true
  }
}

# Create ALB Target Group for backend
resource "aws_lb_target_group" "backend" {
  name     = "prod-claim-app-backend-tg"
  port     = 80
  protocol = "HTTP"
  vpc_id   = module.vpc.id

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    timeout             = 5
    path                = "/health"
    port                = "traffic-port"
    unhealthy_threshold = 2
  }
}

# Add scaling policies (based on CPU utilization)
resource "aws_autoscaling_policy" "backend_scale_up" {
  name                   = "prod-claim-app-backend-scale-up"
  autoscaling_group_name = aws_autoscaling_group.backend.name
  adjustment_type        = "ChangeInCapacity"
  scaling_adjustment     = 1
  cooldown              = 300
  policy_type           = "SimpleScaling"
}

resource "aws_cloudwatch_metric_alarm" "backend_cpu_high" {
  alarm_name          = "prod-claim-app-backend-cpu-high"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name        = "CPUUtilization"
  namespace          = "AWS/EC2"
  period            = 300
  statistic         = "Average"
  threshold         = 80

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.backend.name
  }

  alarm_description = "Scale up if CPU > 80% for 10 minutes"
  alarm_actions     = [aws_autoscaling_policy.backend_scale_up.arn]
}

# Scale down policy
resource "aws_autoscaling_policy" "backend_scale_down" {
  name                   = "prod-claim-app-backend-scale-down"
  autoscaling_group_name = aws_autoscaling_group.backend.name
  adjustment_type        = "ChangeInCapacity"
  scaling_adjustment     = -1
  cooldown              = 300
  policy_type           = "SimpleScaling"
}

resource "aws_cloudwatch_metric_alarm" "backend_cpu_low" {
  alarm_name          = "prod-claim-app-backend-cpu-low"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 2
  metric_name        = "CPUUtilization"
  namespace          = "AWS/EC2"
  period            = 300
  statistic         = "Average"
  threshold         = 30

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.backend.name
  }

  alarm_description = "Scale down if CPU < 30% for 10 minutes"
  alarm_actions     = [aws_autoscaling_policy.backend_scale_down.arn]
}