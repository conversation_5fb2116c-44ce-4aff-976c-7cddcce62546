# AWS Services Cost Estimation - Tradesman Claim App Production Environment

## CI/CD Pipeline Services

| Service | Configuration | Estimated Monthly Cost |
|---------|--------------|------------------------|
| AWS CodePipeline | 2 pipelines (frontend, backend) | $2.00 ($1.00 per pipeline) |
| AWS CodeBuild | 2 projects, BUILD_GENERAL1_SMALL, ~30 builds/month, 10 min avg | $9.00 |
| AWS CodeDeploy | 2 applications, ~15 deployments/month | $0.00 (free) |
| AWS CodeStar Connection | GitHub connection | $0.00 (free) |
| S3 Bucket | Pipeline artifacts, ~5 GB storage | $0.12 |

## Compute Resources

| Service | Configuration | Estimated Monthly Cost |
|---------|--------------|------------------------|
| EC2 Instances (Frontend) | 2 t3.small instances, 10GB storage each | $30.46 |
| EC2 Instances (Backend) | 2 t3.small instances, 50GB storage each | $38.46 |
| Auto Scaling Groups | 2 ASGs | $0.00 (free) |
| Application Load Balancers | 2 ALBs (frontend, backend) | $36.72 |

## Database

| Service | Configuration | Estimated Monthly Cost |
|---------|--------------|------------------------|
| RDS MySQL | db.m7g.large, Multi-AZ, 20GB storage | $262.80 |

## Other Services

| Service | Configuration | Estimated Monthly Cost |
|---------|--------------|------------------------|
| VPC | NAT Gateway, VPN Connection | $32.40 |
| CloudWatch | Logs, metrics, alarms | $10.00 |
| KMS | Key for RDS encryption | $1.00 |
| Secrets Manager | DB credentials | $0.40 |
| IAM | Roles and policies | $0.00 (free) |

## Total Estimated Monthly Cost

**Approximate Total: $423.36/month**

### Notes:
- Costs are estimates based on AWS pricing as of current date
- Actual costs may vary based on usage patterns, data transfer, and region-specific pricing
- Cost optimization opportunities:
  - Use Reserved Instances for EC2 and RDS to reduce costs by 30-60%
  - Implement auto-scaling based on time schedules to reduce instances during off-hours
  - Monitor and adjust resources based on actual usage patterns