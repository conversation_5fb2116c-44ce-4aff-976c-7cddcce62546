#!/bin/bash

# Enable strict error handling
set -euo pipefail

# Logging function
log() {
  local level="$1"
  local message="$2"
  local timestamp
  timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] [$level] $message" | tee -a "/usr/local/bin/scripts/script_execution.log"
}

# Variables
timestamp=$(date +"%Y-%m-%d_%H-%M-%S")
OUTPUT_DIR="/usr/local/bin/scripts/exported_csv_files"
CSV_FILE="${OUTPUT_DIR}/exported_data_$timestamp.csv"
TARGET_MYSQL_DATABASE="claim_app"
TARGET_MYSQL_STAGE_TABLE="claim_form_data_staging"
TARGET_MYSQL_FINAL_TABLE="claim_form_data"
MYSQL_LOGIN_PATH="snowflake_tradesman"
LOG_FILE="/usr/local/bin/scripts/script_execution.log"

# Start logging to file
log "INFO" "Starting script execution..."

# Query for selecting the records
QUERY=$(cat <<EOF
SELECT
    ROW_NUMBER() OVER (ORDER BY NULL) AS id,
    0 AS law_firm_id,
    BROKER_ID,
BROKER_NAME,
CASE_ANALYSIS,
CASE_FACTS,
CASE_NAME,
CASE_OF_CONCERN_TO_SENIOR_MANAGEMENT,
CASE_TO_TRY_OR_CASE_TO_SETTLE,
CLAIM_ADJUSTER,
CLAIM_NUMBER,
CLAIM_STATUS,
CLAIM_SUPERVISOR,
CLAIM_TYPE,
CLAIMANT_NAME,
TO_CHAR(TO_DATE(DATE_OF_LOSS, 'MM/DD/YY'), 'YYYY-MM-DD') AS DATE_OF_LOSS,
DATE_OF_LOSS_NOTES,
TO_CHAR(TO_DATE(EFFECTIVE_DATE, 'MM/DD/YY'), 'YYYY-MM-DD') AS EFFECTIVE_DATE,
EXPENSE_INCURRED,
EXPENSE_OUTSTANDING,
EXPENSE_PAID,
FIRM_MATTER_NUMBER,
INDEMNITY_INCURRED,
INDEMNITY_OUTSTANDING,
INDEMNITY_PAID,
INJURY_LOSS,
INSURED_DEFENDANT_NAME,
LITIGATION_PHASE,
LOSS_DESCRIPTION,
MEDICAL_INCURRED,
MEDICAL_OUTSTANDING,
MEDICAL_PAID,
MEDICAL_PROVIDER,
OTHER_INCURRED,
OTHER_OUTSTANDING,
OTHER_PAID,
PARALEGAL,
PLAINTIFF_ATTORNEY,
PLAINTIFF_LAW_FIRM,
PLAINTIFF_NAME,
POLICY_NUMBER,
POTENTIAL_CASE_EXPOSURE,
PRODUCER,
PROPERTY_DAMAGE_INCURRED,
PROPERTY_DAMAGE_OUTSTANDING,
PROPERTY_DAMAGE_PAID,
RESERVE_INCREASE_REQUESTED,
SURGEON,
TRADESMAN_COMMENTS,
TO_CHAR(TO_DATE(TRIAL_DATE, 'MM/DD/YY'), 'YYYY-MM-DD') AS TRIAL_DATE,
TRIAL_INFORMATION,
VENUE,
DEFENSE_COUNSEL,
FROM RAW.REFERENCE.SAMPLE_DATA_CLAIMS_APP;
EOF
)

# Create output directory if not exists
log "INFO" "Checking and creating output directory..."
mkdir -p "$OUTPUT_DIR"
log "INFO" "Output directory is set to $OUTPUT_DIR."

# Export query result to CSV using SnowSQL
log "INFO" "Exporting data from Snowflake..."
/home/<USER>/bin/snowsql -q "$QUERY" \
        -o output_format=csv \
        -o header=false \
        -o friendly=false \
        -o timing=false \
        -o output_file="$CSV_FILE"

log "INFO" "Data exported successfully to $CSV_FILE."

# Connect to MySQL and truncate the staging table
log "INFO" "Connecting to MySQL to truncate the staging table: $TARGET_MYSQL_STAGE_TABLE..."
/usr/bin/mysql --login-path="$MYSQL_LOGIN_PATH" \
      -D "$TARGET_MYSQL_DATABASE" \
      -e "TRUNCATE TABLE $TARGET_MYSQL_STAGE_TABLE;"
log "INFO" "Table $TARGET_MYSQL_STAGE_TABLE truncated successfully."

# Load CSV into MySQL staging table using LOAD DATA LOCAL INFILE
log "INFO" "Loading data from $CSV_FILE into MySQL table $TARGET_MYSQL_STAGE_TABLE..."
/usr/bin/mysql --login-path="$MYSQL_LOGIN_PATH" \
      --local_infile=1 \
      -D "$TARGET_MYSQL_DATABASE" \
      -e "LOAD DATA LOCAL INFILE '$(realpath "$CSV_FILE")'
          INTO TABLE $TARGET_MYSQL_STAGE_TABLE
          FIELDS TERMINATED BY ','
          OPTIONALLY ENCLOSED BY '\"'
          LINES TERMINATED BY '\n';"
log "INFO" "Data loaded successfully into MySQL table $TARGET_MYSQL_STAGE_TABLE."



# Insert new rows into the final table from the staging table using LEFT JOIN and SELECT *
log "INFO" "Inserting new rows from staging table $TARGET_MYSQL_STAGE_TABLE into final table $TARGET_MYSQL_FINAL_TABLE..."

/usr/bin/mysql --login-path="$MYSQL_LOGIN_PATH" \
      -D "$TARGET_MYSQL_DATABASE" \
      -e "
        INSERT INTO $TARGET_MYSQL_FINAL_TABLE
        SELECT s.*
        FROM $TARGET_MYSQL_STAGE_TABLE s
        LEFT JOIN $TARGET_MYSQL_FINAL_TABLE f ON s.claim_number = f.claim_number
        WHERE f.claim_number IS NULL;"
log "INFO" "New rows successfully inserted into final table $TARGET_MYSQL_FINAL_TABLE."

# End of script
log "INFO" "Script execution completed successfully."
