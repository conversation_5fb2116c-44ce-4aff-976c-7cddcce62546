#!/bin/bash

# Check if at least one argument was provided
if [ $# -eq 0 ]; then
    echo "Error: No arguments provided"
    echo "Usage: $0 <argument1> [argument2...]"
    exit 1
fi

# The predefined command part
PREDEFINED_CMD="--profile ds-tradesman"

# Join all input arguments with spaces
INPUT_ARGS="$*"

# Construct the full command by prepending all arguments
FULL_CMD="aws $INPUT_ARGS $PREDEFINED_CMD"

# Execute the command (or just print it for demonstration)
echo "Executing: $FULL_CMD"
# If you actually want to execute it, uncomment the next line:
eval "$FULL_CMD"