# Fivetran MySQL to Snowflake Integration Guide

This guide explains how to configure Fivetran to pull data directly from a MySQL database and load it into the `claim_application` schema in Snowflake.

---

## Option 1: MySQL Direct Connector

### Prerequisites
- Access to Fivetran account (https://fivetran.com)
- MySQL database credentials (host, port, user, password, database name)
- Snowflake account and credentials
- Network access between Fivetran and your MySQL server (allowlist Fivetran IPs)

### Step 1: Create a MySQL Connector in Fivetran
1. Log in to your Fivetran dashboard.
2. Click **Connectors** > **Add Connector**.
3. Choose **MySQL** as the source.
4. Enter your MySQL connection details:
   - Host
   - Port
   - Database
   - Username
   - Password
5. (Optional) For secure connection, enable SSL.
6. Click **Save & Test** to verify the connection.

### Step 2: Configure Incremental Sync
- Fivetran will automatically detect tables and suggest a sync method.
- For large tables, ensure you have an `updated_at` or similar timestamp column for incremental sync.
- In the connector setup, select the columns/tables to sync and set the incremental column if needed.

### Step 3: Set the Destination to Snowflake
1. In the connector setup, choose your Snowflake destination.
2. Set the **schema name** to `claim_application` (or your desired schema).
3. Map source tables to destination tables as needed.

### Step 4: Start the Connector
- Click **Start Sync** to begin the initial load.
- Monitor progress in the Fivetran dashboard.

### Step 5: Monitor and Troubleshoot
- Check the Fivetran dashboard for sync status and error messages.
- Review logs in the dashboard or in your script's `.log` file if using the API.
- Ensure your MySQL server allows connections from Fivetran's IP addresses (see Fivetran documentation for the list).

---

## Option 2: S3 CSV Connector (Pulling CSV Exports from S3)

If you are exporting your MySQL data as CSV files to S3 (e.g., using `script_3.sh`), you can configure Fivetran to ingest these files and update your Snowflake schema.

### Prerequisites
- Access to Fivetran account
- AWS S3 bucket with exported CSV files (e.g., `s3://claimapp-backup/mysql_export/`)
- AWS IAM user or role with permissions to read from the S3 bucket
- Snowflake account and credentials

### Step 1: Prepare S3 Bucket and Permissions
1. Ensure your exported CSV files are uploaded to your S3 bucket (the script does this automatically).
2. Create or use an AWS IAM user/role with the following permissions for the bucket:
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "s3:GetObject",
           "s3:ListBucket"
         ],
         "Resource": [
           "arn:aws:s3:::claimapp-backup",
           "arn:aws:s3:::claimapp-backup/mysql_export/*"
         ]
       }
     ]
   }
   ```
3. Note the AWS Access Key ID and Secret Access Key for this user/role.

### Step 2: Create an S3 Connector in Fivetran
1. In the Fivetran dashboard, click **Connectors** > **Add Connector**.
2. Choose **Amazon S3** as the source.
3. Enter your S3 bucket details:
   - Bucket name: `claimapp-backup`
   - Folder path: `mysql_export/`
   - AWS credentials (Access Key ID and Secret Access Key)
4. Configure the file format:
   - File type: CSV
   - Delimiter: `,`
   - Text qualifier: `"`
   - Compression: (if used)
   - Header row: Yes (first row is header)
5. Set up the sync frequency as desired.
6. Click **Save & Test** to verify the connection.

### Step 3: Set the Destination to Snowflake
1. Choose your Snowflake destination.
2. Set the **schema name** to `claim_application` (or your desired schema).
3. Map the S3 CSV data to the appropriate table(s) in Snowflake.
   - You may need to configure column mapping if the CSV headers differ from your Snowflake table columns.

### Step 4: Start the Connector
- Click **Start Sync** to begin the initial load.
- Monitor progress in the Fivetran dashboard.

### Step 5: Monitor and Troubleshoot
- Check the Fivetran dashboard for sync status and error messages.
- Ensure your S3 bucket and IAM permissions are correct.
- Review logs in the dashboard or in your script's `.log` file.

---

## Tips
- For best performance, use a dedicated IAM user or role for Fivetran S3 access.
- If syncing large files, consider splitting them or increasing sync frequency.
- Use Fivetran's notifications to alert you of sync failures.

## References
- [Fivetran MySQL Connector Docs](https://fivetran.com/docs/databases/mysql)
- [Fivetran S3 Connector Docs](https://fivetran.com/docs/files/s3)
- [Fivetran Snowflake Destination Docs](https://fivetran.com/docs/destinations/snowflake) 