#!/bin/bash

set -euo pipefail

# Logging function
log() {
  local level="$1"
  local message="$2"
  local timestamp
  timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] [$level] $message" | tee -a "./fivetran_sync.log"
}

# Fivetran API credentials and connector info
FIVETRAN_API_KEY="YOUR_API_KEY"
FIVETRAN_API_SECRET="YOUR_API_SECRET"
CONNECTOR_ID="YOUR_CONNECTOR_ID"
LOG_FILE="./fivetran_sync.log"
BATCH_SIZE=1000

# Function to trigger Fivetran sync
trigger_sync() {
  log "INFO" "Triggering Fivetran sync for connector $CONNECTOR_ID..."
  RESPONSE=$(curl -s -X POST "https://api.fivetran.com/v1/connectors/$CONNECTOR_ID/force" \
    -u "$FIVETRAN_API_KEY:$FIVETRAN_API_SECRET")
  log "INFO" "Sync triggered. Response: $RESPONSE"
}

# Function to poll Fivetran sync status
poll_sync_status() {
  while true; do
    STATUS=$(curl -s -X GET "https://api.fivetran.com/v1/connectors/$CONNECTOR_ID" \
      -u "$FIVETRAN_API_KEY:$FIVETRAN_API_SECRET" | jq -r '.data.status.sync_state')
    log "INFO" "Current sync state: $STATUS"
    if [[ "$STATUS" == "succeeded" ]]; then
      log "INFO" "Sync completed successfully."
      break
    elif [[ "$STATUS" == "failed" ]]; then
      log "ERROR" "Sync failed. Check Fivetran dashboard for details."
      exit 1
    fi
    sleep 30
  done
}

# Function to fetch sync report
fetch_sync_report() {
  REPORT=$(curl -s -X GET "https://api.fivetran.com/v1/connectors/$CONNECTOR_ID" \
    -u "$FIVETRAN_API_KEY:$FIVETRAN_API_SECRET")
  echo "$REPORT" >> "$LOG_FILE"
  log "INFO" "Sync report saved to $LOG_FILE."
}

# Main loop to trigger syncs in batches
log "INFO" "Starting batched Fivetran syncs (batch size: $BATCH_SIZE)..."

# Note: Fivetran handles batching via incremental syncs. To process in intervals, you may need to adjust the source table or use a view/column for incremental loading.
# This script triggers syncs in a loop, assuming the connector is configured for incremental sync (e.g., using an updated_at column).

for ((i=1; i<=BATCH_SIZE; i++)); do
  trigger_sync
  poll_sync_status
  fetch_sync_report
  log "INFO" "Batch $i completed."
  # Optionally, add logic to stop if all data is synced
  # sleep 60  # Wait before next batch if needed
  # You may want to check if there is more data to sync

done

log "INFO" "All batches completed. Check $LOG_FILE for details." 