#!/bin/bash

# Enable strict error handling
set -euo pipefail

# Logging function
log() {
  local level="$1"
  local message="$2"
  local timestamp
  timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] [$level] $message" | tee -a "./script_execution.log"
}

# Variables
timestamp=$(date +"%Y-%m-%d_%H-%M-%S")
OUTPUT_DIR="./exported_csv_files"
CSV_FILE="${OUTPUT_DIR}/exported_data_$timestamp.csv"
TARGET_MYSQL_DATABASE="claimappdb"
TARGET_MYSQL_STAGE_TABLE="claim_form_data_staging"
TARGET_MYSQL_FINAL_TABLE="claim_form_data"
MYSQL_LOGIN_PATH="snowflake_tradesman"  # Login path for MySQL
LOG_FILE="./script_execution.log"

# Start logging to file
log "INFO" "Starting script execution..."

# Query for selecting the records
QUERY=$(cat <<EOF
SELECT
    ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS id,
    0 AS law_firm_id,
    BROKER_ID,
    BROKER_NAME,
    CASE_ANALYSIS,
    CASE_FACTS,
    CASE_NAME,
    IS_CASE_OF_CONCERN_TO_SENIOR_MANAGEMENT,
    IS_CASE_TO_TRY_OR_CASE_TO_SETTLE,
    CLAIM_ADJUSTER,
    CLAIM_NUMBER,
    CLAIM_STATUS,
    CLAIM_TYPE,
    CLAIMANT_NAME,
    DATE_OF_LOSS,
    DATE_OF_LOSS_NOTES,
    EFFECTIVE_DATE,
    EXPENSE_INCURRED,
    EXPENSE_OUTSTANDING,
    EXPENSE_PAID,
    FIRM_MATTER_NUMBER,
    INDEMNITY_INCURRED,
    INDEMNITY_OUTSTANDING,
    INDEMNITY_PAID,
    INJURY_OR_DAMAGES,
    REPORTING_UNIT_NAME,
    LITIGATION_PHASE,
    LOSS_DESCRIPTION,
    MEDICAL_PROVIDERS,
    PLAINTIFF_ATTORNEY,
    PLAINTIFF_LAW_FIRM,
    POLICY_NUMBER,
    PROPERTY_DAMAGES_INCURRED,
    PROPERTY_DAMAGES_OUTSTANDING,
    PROPERTY_DAMAGES_PAID,
    RESERVE_INCREASE_REQUESTED,
    SURGEONS,
    TRADESMAN_COMMENTS,
    TRIAL_DATE,
    TRIAL_INFORMATION,
    VENUE,
    DEFENSE_COUNSEL,
    ACCIDENT_STATE,
    EXPIRATION_DATE,
    WORK_COMP_LIEN,
    TOTAL_AMOUNT_MEDICALS,
    OTHER_DAMAGES,
    LAWYER_ADDITIONAL_COMMENTS,
    SETTLEMENT_VALUE,
    VERDICT_VALUE,
    PROPOSED_RESERVE_INCREASE,
    FIRST_CHAIR,
    SECOND_CHAIR,
    POTENTIAL_240_1_CASE,
    INDUSTRIAL_VIOLATION_CODE_ALLEGED,
    IS_THERE_VIOLATION_CODE_IN_YOUR_OPINION
FROM PRODUCTION.ANALYTICS_REVERSE_ETL.REVERSE_ETL_CLAIM_APP_DATA_FEED;
EOF
)

# Create output directory if not exists
log "INFO" "Checking and creating output directory..."
mkdir -p "$OUTPUT_DIR"
log "INFO" "Output directory is set to $OUTPUT_DIR."

# Export query result to CSV using SnowSQL
log "INFO" "Exporting data from Snowflake..."
/home/<USER>/bin/snowsql -q "$QUERY" \
        -o output_format=csv \
        -o header=false \
        -o friendly=false \
        -o timing=false \
        -o output_file="$CSV_FILE"
        
log "INFO" "Data exported successfully to $CSV_FILE."

# Connect to MySQL and truncate the staging table
log "INFO" "Connecting to MySQL to truncate the staging table: $TARGET_MYSQL_STAGE_TABLE..."
/usr/bin/mysql --login-path="$MYSQL_LOGIN_PATH" \
      -D "$TARGET_MYSQL_DATABASE" \
      -e "TRUNCATE TABLE $TARGET_MYSQL_STAGE_TABLE;"
log "INFO" "Table $TARGET_MYSQL_STAGE_TABLE truncated successfully."

# Load CSV into MySQL staging table using LOAD DATA LOCAL INFILE
log "INFO" "Loading data from $CSV_FILE into MySQL table $TARGET_MYSQL_STAGE_TABLE..."
/usr/bin/mysql --login-path="$MYSQL_LOGIN_PATH" \
      --local_infile=1 \
      -D "$TARGET_MYSQL_DATABASE" \
      -e "LOAD DATA LOCAL INFILE '$(realpath "$CSV_FILE")' 
          INTO TABLE $TARGET_MYSQL_STAGE_TABLE 
          FIELDS TERMINATED BY ',' 
          OPTIONALLY ENCLOSED BY '\"' 
          LINES TERMINATED BY '\n';"
log "INFO" "Data loaded successfully into MySQL table $TARGET_MYSQL_STAGE_TABLE."



# Insert new rows into the final table from the staging table using LEFT JOIN and SELECT *
log "INFO" "Inserting new rows from staging table $TARGET_MYSQL_STAGE_TABLE into final table $TARGET_MYSQL_FINAL_TABLE..."

/usr/bin/mysql --login-path="$MYSQL_LOGIN_PATH" \
      -D "$TARGET_MYSQL_DATABASE" \
      -e "
      INSERT INTO $TARGET_MYSQL_FINAL_TABLE (
law_firm_id,
broker_id,
broker_name,
case_analysis,
case_fact,
case_name,
case_concern_to_sm,
ctt_or_cts,
claim_adjuster,
claim_number,
claim_status,
claim_type,
claimant_name,
date_of_loss,
date_of_loss_notes,
effective_date,
expense_incurred,
expense_outstanding,
expense_paid,
firm_matter_number,
indemnity_incurred,
indemnity_outstanding,
indemnity_paid,
injury_loss,
insured_defendant_name,
litigation_phase,
loss_description,
medical_provider,
plaintiff_attorny,
plaintiff_law_firm,
policy_number,
property_damage_incurred,
property_damage_outstanding,
property_damage_paid,
reverse_increase_requested,
surgeon,
tradesman_comments,
trail_date,
trail_information,
venue,
defense_counsel,
accident_state,
expiration_date,
work_comp_lien,
total_amount_medicals,
other_demages,
layer_aditional_comments,
settlement_value,
verdict_value,
propose_reserve_increase,
1st_chair,
2nd_chair,
potential_case,
industrial_code_voilation_alleged,
industrial_code_voilation_opinion,
deleted_at,
created_at,
updated_at
)
        SELECT 
        s.law_firm_id,
    s.broker_id,
    s.broker_name,
    s.case_analysis,
    s.case_fact,
    s.case_name,
    s.case_concern_to_sm,
    s.ctt_or_cts,
    s.claim_adjuster,
    s.claim_number,
    s.claim_status,
    s.claim_type,
    s.claimant_name,
    s.date_of_loss,
    s.date_of_loss_notes,
    s.effective_date,
    s.expense_incurred,
    s.expense_outstanding,
    s.expense_paid,
    s.firm_matter_number,
    s.indemnity_incurred,
    s.indemnity_outstanding,
    s.indemnity_paid,
    s.injury_loss,
    s.insured_defendant_name,
    s.litigation_phase,
    s.loss_description,
    s.medical_provider,
    s.plaintiff_attorny,
    s.plaintiff_law_firm,
    s.policy_number,
    s.property_damage_incurred,
    s.property_damage_outstanding,
    s.property_damage_paid,
    s.reverse_increase_requested,
    s.surgeon,
    s.tradesman_comments,
    NULLIF(s.trail_date, '0000-00-00'),
    s.trail_information,
    s.venue,
    s.defense_counsel,
    s.accident_state,
    s.expiration_date,
    s.work_comp_lien,
    s.total_amount_medicals,
    s.other_demages,
    s.layer_aditional_comments,
    s.settlement_value,
    s.verdict_value,
    s.propose_reserve_increase,
    s.1st_chair,
    s.2nd_chair,
    s.potential_case,
    s.industrial_code_voilation_alleged,
    s.industrial_code_voilation_opinion,
    s.deleted_at,
    s.created_at,
    s.updated_at
        FROM $TARGET_MYSQL_STAGE_TABLE s
        LEFT JOIN $TARGET_MYSQL_FINAL_TABLE f ON s.claim_number = f.claim_number
        WHERE f.claim_number IS NULL;"
log "INFO" "New rows successfully inserted into final table $TARGET_MYSQL_FINAL_TABLE."

# End of script
log "INFO" "Script execution completed successfully."
