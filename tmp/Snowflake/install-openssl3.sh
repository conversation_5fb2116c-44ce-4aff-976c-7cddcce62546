#!/bin/bash
# OpenSSL 3 Installation Script for Ubuntu 24.04 (Noble Numbat)
# Compatible with SnowSQL requirements
# Based on https://hostnextra.com/learn/tutorials/how-to-install-openssl-3-on-ubuntu

set -e  # Exit on error
set -x  # Print commands

# 1. Check current OpenSSL version
echo "Current OpenSSL version:"
openssl version

# 2. Install prerequisites
sudo apt update
sudo apt install -y \
    build-essential \
    checkinstall \
    zlib1g-dev \
    libpcre3-dev \
    libssl-dev \
    perl \
    wget \
    git \
    gcc \
    make

# 3. Download OpenSSL 3 (latest stable version)
OPENSSL_VERSION="3.2.1"  # Update to latest stable version
wget https://www.openssl.org/source/openssl-${OPENSSL_VERSION}.tar.gz
tar -xf openssl-${OPENSSL_VERSION}.tar.gz
cd openssl-${OPENSSL_VERSION}

# 4. Configure and compile
./config --prefix=/usr/local/ssl --openssldir=/usr/local/ssl shared zlib
make -j$(nproc)
make test

# 5. Install without replacing system OpenSSL (safer approach)
sudo make install

# 6. Configure library paths
echo "/usr/local/ssl/lib64" | sudo tee /etc/ld.so.conf.d/openssl-3.conf
sudo ldconfig -v

# 7. Create symbolic links for development
sudo ln -sf /usr/local/ssl/bin/openssl /usr/local/bin/openssl
sudo ln -sf /usr/local/ssl/include/openssl /usr/include/openssl

# 8. Verify installation
echo "New OpenSSL version:"
/usr/local/ssl/bin/openssl version

# 9. Configure environment variables (for SnowSQL compatibility)
echo 'export PATH="/usr/local/ssl/bin:$PATH"' >> ~/.bashrc
echo 'export LD_LIBRARY_PATH="/usr/local/ssl/lib64:$LD_LIBRARY_PATH"' >> ~/.bashrc
source ~/.bashrc

# 10. Clean up
cd ..
rm -rf openssl-${OPENSSL_VERSION}*