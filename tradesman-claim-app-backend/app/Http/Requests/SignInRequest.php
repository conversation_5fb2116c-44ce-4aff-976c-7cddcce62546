<?php

namespace App\Http\Requests;

use App\Rules\CheckSignInSoftDeletedUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class SignInRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
           'email' =>[ 'required','email','exists:user','max:255', new CheckSignInSoftDeletedUser()],
            'password' => 'required|regex:/^(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/',
        ];
    }
     /**
     * Get the custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'email.required' => 'The email field is required.',
            'email.email' => 'Please provide a valid email address.',
            'email.exists' => 'The provided e-mail is not registered ',
            'password.required' => 'The password field is required.',
            'password.regex' => '8 + characters, with min one upercase letter, one number and one special charcter'
        ];
    }
    /**
	 * Custom JSON response if validation failed
	 */
	protected function failedValidation(Validator $validator) {
		throw new HttpResponseException(
			response()->json([
				'status' => 'Error',
                'messages' => $validator->errors()->all(),
                'data' => null
			], 200)
		);
	}
}
