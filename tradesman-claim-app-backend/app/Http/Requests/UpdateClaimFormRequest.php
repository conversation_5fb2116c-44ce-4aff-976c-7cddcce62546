<?php

namespace App\Http\Requests;

use App\Services\PermissionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateClaimFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    protected $permissionService;
    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // $caseFields = [
        //     'case_analysis', 'case_fact', 'case_name', 'case_concern_to_sm', 'ctt_or_cts', 'claim_type',
        //     'date_of_loss_notes', 'injury_loss', 'litigation_phase', 'medical_provider', 'plaintiff_attorny',
        //     'plaintiff_law_firm', 'reverse_increase_requested', 'surgeon', 'tradesman_comments', 'trail_date',
        //     'trail_information', 'venue', 'defense_counsel', 'work_comp_lien', 'other_demages', 
        //     'layer_aditional_comments', 'settlement_value', 'verdict_value', 'propose_reserve_increase', 
        //     '1st_chair', '2nd_chair', 'potential_case', 'industrial_code_voilation_alleged', 
        //     'industrial_code_voilation_opinion','firm_matter_number'
        // ];
        // $user = Auth::user();
        // $editable_columns = collect($this->permissionService->edit_able_columns($user))->toArray();
        // array_push($editable_columns, 'id');
        // $rules = [];
        // foreach ($editable_columns as $field)
        // {
        //     if ($field === 'id')
        //     {
        //         $rules[$field] = 'required|exists:claim_form_data,id';
        //     }
        //     else
        //     {
        //         if ($field == 'effective_date' || $field == 'expiration_date')
        //         {
        //             $rules[$field] = 'required|date_format:Y-m-d';
        //         }
        //         elseif (!in_array($field, $caseFields))
        //         {
        //             $rules[$field] = 'required';
        //         }else{
        //             $rules[$field] = 'nullable';
        //         }
        //     }
        // }
        // return $rules;
        return [
            'id' => 'required|exists:claim_form_data,id',
        ];
    }
    /**
     * Custom JSON response if validation failed
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'Error',
                'messages' => $validator->errors()->all(),
                'data' => null
            ], 200)
        );
    }
}
