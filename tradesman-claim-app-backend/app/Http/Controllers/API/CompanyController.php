<?php

namespace App\Http\Controllers\API;

use App\Models\LawFirm;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Resources\LawFirmResource;
use App\Http\Requests\CreateCompanyRequest;
use App\Http\Requests\CompanyDetailsRequest;
use App\Http\Requests\UpdateCompanyDetailsRequest;

class CompanyController extends Controller
{
    use ApiResponser;
    public function index(Request $request)
    {
        try
        {
            $companiesQuery = LawFirm::query();

            if ($request->has('search') && !empty($request->input('search')))
            {
                $search_value = $request->input('search');
                $companiesQuery->where(function ($query) use ($search_value)
                {
                    $query->where('title', 'LIKE', "%{$search_value}%");
                });
            }

            $offset = $request->input('offset', 0);
            $limit = $request->input('limit', 10);
            $total_records = $companiesQuery->count();
            $companies = $companiesQuery->offset($offset)->limit($limit)->get();
            $companies_data = [
                'companies_data' => $companies, 
                'pagination' => [
                    'total' => $total_records,
                    'offset' => $offset,
                    'limit' => $limit,
                ],
            ];
            return $this->success('Companies data fetch successfully', LawFirmResource::collection($companies_data), 200);
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    public function create(CreateCompanyRequest $request)
    {
        try
        {
            DB::beginTransaction();
            $company = new LawFirm();
            $company->create([
                'title' => $request->title
            ]);
            DB::commit();

            return $this->success("Company created successfully", [], 200);
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong' , 400);
        }
    }

    public function company_details(CompanyDetailsRequest $request)
    {
        try
        {
            $company = LawFirm::where('id', $request->id)->first();
            if (!empty($company))
            {
                return $this->success('Company details fetch successfully', $company, 200);
            }
            else
            {
                return $this->success('No company found!', [], 200);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }
    public function update_company(UpdateCompanyDetailsRequest $request)
    {
        try
        {
            $company = LawFirm::where('id', $request->id)->first();
            if (!empty($company))
            {
                DB::beginTransaction();
                $company->update([
                    'title' => $request->title
                ]);
                DB::commit();
                return $this->success("Company details updated successfully", [], 200);
            }
            else
            {
                return $this->success('No company found!', [], 200);
            }
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }
    public function delete_comapny(CompanyDetailsRequest $request)
    {
        try
        {
            $company = LawFirm::with('company_users')->where('id', $request->id)->first();
            if ($company && $company->company_users->isEmpty())
            {
                DB::beginTransaction();
                $company->delete();
                DB::commit();
                return $this->success("Company details deleted successfully", [], 200);
            }
            else
            {
                return $this->error('The company you are trying to delete belongs to some users', 200);
            }
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }
}
