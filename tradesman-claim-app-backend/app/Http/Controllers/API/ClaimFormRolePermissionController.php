<?php

namespace App\Http\Controllers\API;

use App\Models\ClaimForm;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Services\PermissionService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\ClaimFormRequest;
use App\Models\ClaimFormRolePermission;
use App\Http\Requests\AssignClaimsRequest;
use App\Http\Resources\ClaimAppDataResource;
use App\Http\Requests\UpdateClaimFormRequest;
use App\Http\Resources\UnAssignClaimResource;
use App\Http\Resources\ClaimAppDetailsResource;

class ClaimFormRolePermissionController extends Controller
{
    use ApiResponser;
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, PermissionService $permissionService)
    {
        try
        {
            $user = Auth::user();
            $compay_id = Auth::user()->law_firm_id;
            $user['permissions'] = collect($permissionService->user_permissions($user));
            $viewable_columns = $user['permissions']->where('is_viewable', 'Y')->pluck('field_name')->toArray();
            array_push($viewable_columns, 'id');

            $query_data = ClaimForm::with(['claims_firm' => function ($query) {
                $query->select('id', 'title'); 
            }]);
            if (Auth::user()->role_type_id == 1)
            {
                $claim_data = $query_data->select($viewable_columns)->where('law_firm_id', '!=', 0);
            }
            else
            {
                $claim_data = $query_data->select($viewable_columns)->where('law_firm_id', $compay_id)->where('law_firm_id', '!=', 0);
            }

            if ($request->has('claim_status') && !empty($request->input('claim_status')))
            {
                $status = $request->input('claim_status');
                $claim_data->where(function ($query) use ($status)
                {
                    $query->orWhere('claim_status', 'LIKE', "%{$status}%");
                });
            }

            if ($request->has('search') && !empty($request->input('search')))
            {
                $search_value = $request->input('search');
                $claim_data->where(function ($query) use ($viewable_columns, $search_value)
                {
                    foreach ($viewable_columns as $column)
                    {
                        $query->orWhere($column, 'LIKE', "%{$search_value}%");
                    }
                })
                ->orWhereHas('claims_firm', function ($query) use ($search_value) {
                    $query->where('title', 'LIKE', "%{$search_value}%");
                });
            }

            $total_records = $claim_data->count();
            $offset = $request->input('offset', 0);
            $limit = $request->input('limit', 10);

            $claim_form_data = $claim_data->offset($offset)->limit($limit)->get();
            $response_data = [
                'claim_form_data' =>ClaimAppDataResource::collection($claim_form_data),
                'pagination' => [
                    'total' => $total_records,
                    'offset' => $offset,
                    'limit' => $limit,
                ],
            ];

            return $this->success('Claim data fetched successfully', $response_data, 200);
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ClaimFormRolePermission $claimFormRolePermission)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ClaimFormRequest $request, PermissionService $permissionService)
    {
        try
        {
            $user = Auth::user();
            $compay_id = Auth::user()->law_firm_id;
            $id = $request->id;
            $editable_columns = collect($permissionService->edit_able_columns($user))->toArray();
            array_push($editable_columns, 'id');
            $claim_data = ClaimForm::select($editable_columns)->where('law_firm_id', $compay_id)->where('id', $id)->first();
            if ($claim_data)
            {
                return $this->success('Claim data fetch successfully', ClaimAppDetailsResource::collection([$claim_data]), 200);
            }
            else
            {
                return $this->success('No Claim found!', [], 200);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateClaimFormRequest $request,  PermissionService $permissionService)
    {
        try
        {
            $payload = $request->all();
            $user = Auth::user();
            $editable_columns = collect($permissionService->edit_able_columns($user))->toArray();
            $allowed_columns = [];
            foreach ($payload as $key => $value)
            {
                if (in_array($key, $editable_columns))
                {
                    $allowed_columns[$key] = $value;
                }
            }
            if (!empty($allowed_columns))
            {
                DB::beginTransaction();
                $claim_data = new ClaimForm();
                $claim_data->where('id', $request->id)
                    ->update($allowed_columns);
                DB::commit();
                return $this->success('Claim data updated successfully', [], 200);
            }
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Get un Assign claims APi.
     */
    public function unassignClaims(Request $request)
    {
        try
        {
            $claims = ClaimForm::where('law_firm_id', 0);
            $total_records = $claims->count();
            $offset = $request->input('offset', 0);
            $limit = $request->input('limit', 10);
            $claims_data = $claims->offset($offset)->limit($limit)->get();
            $response_data = [
                'claims_data' => UnAssignClaimResource::collection($claims_data),
                'pagination' => [
                    'total' => $total_records,
                    'offset' => $offset,
                    'limit' => $limit,
                ],
            ];
            if ($claims_data)
            {
                return $this->success('Un Assign Claim data fetched successfully', $response_data, 200);
            }
            else
            {
                return $this->success('No Claim found!', [], 200);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }
    /**
     * Get un Assign claims APi.
     */
    public function assignClaimsData(AssignClaimsRequest $request)
    {
        try
        {
            $ids = $request->ids;
            $firm_id = $request->firm_id;
            $claim_data = ClaimForm::whereIn('id', $ids)->get();
            if ($claim_data)
            {
                DB::beginTransaction();
                foreach ($claim_data as $data)
                {
                    $data->update([
                        'law_firm_id' => $firm_id
                    ]);
                }
                DB::commit();
                return $this->success('Claims Assign successfully', [], 200);
            }
            else
            {
                return $this->success('No Claim found!', [], 200);
            }
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ClaimFormRolePermission $claimFormRolePermission)
    {
        //
    }
}
