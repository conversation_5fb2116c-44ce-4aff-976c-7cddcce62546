<?php

namespace App\Http\Controllers\API;

use App\Models\User;
use App\Models\RoleType;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\UserDetailsRequest;
use App\Http\Resources\UserDeatilsResource;
use App\Http\Resources\UserListingResource;
use App\Http\Resources\RolesListingResource;
use App\Http\Requests\UpdateUserDetailsRequest;

class UserController extends Controller
{
    use ApiResponser;

    public function index(Request $request)
    {
        try
        {
            $usersQuery = User::with(['user_role', 'user_company']);

            if ($request->has('search') && !empty($request->input('search')))
            {
                $search_value = $request->input('search');
                $usersQuery->where(function ($query) use ($search_value)
                {
                    $query->where('name', 'LIKE', "%{$search_value}%");
                    $query->orWhereHas('user_role', function ($query) use ($search_value)
                    {
                        $query->where('role_name', 'LIKE', "%{$search_value}%");
                    });
                    $query->orWhereHas('user_company', function ($query) use ($search_value)
                    {
                        $query->where('title', 'LIKE', "%{$search_value}%");
                    });
                });
            }
            $offset = $request->input('offset', 0);
            $limit = $request->input('limit', 10);
            $users = $usersQuery->offset($offset)->limit($limit)->get();
            $total_records = $usersQuery->count();
            $user_data = [
                'users_data' => UserListingResource::collection($users), 
                'pagination' => [
                    'total' => $total_records,
                    'offset' => $offset,
                    'limit' => $limit,
                ],
            ];
            if (!empty($users))
            {
                return $this->success('Users data fetch successfully', $user_data, 200);
            }
            else
            {
                return $this->success('No user Found!', []);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }
    public function role_listing()
    {
        try
        {
            $roles = RoleType::where('role_name', '!=', 'Admin')->get();
            if (!empty($roles))
            {
                return $this->success('Roles data fetch successfully', RolesListingResource::collection($roles), 200);
            }
            else
            {
                return $this->success('No Role Found!', []);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    public function get_user(UserDetailsRequest $request)
    {
        try
        {
            $user = User::with(['user_role', 'user_company'])->where('id', $request->id)->first();
            if (!empty($user))
            {
                return $this->success('User data fetch successfully', UserDeatilsResource::collection([$user]), 200);
            }
            else
            {
                return $this->success('No user Found!', []);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }
    public function update_user(UpdateUserDetailsRequest $request)
    {
        try
        {
            $user = User::where('id', $request->id)->first();
            if (!empty($user))
            {
                DB::beginTransaction();
                $user->update([
                    'name' => $request->name,
                    'law_firm_id' => $request->company_id,
                    'role_type_id' => $request->role_id,
                ]);
                DB::commit();
                return $this->success('User details updated successfully', [], 200);
            }
            else
            {
                return $this->success('No user Found!', []);
            }
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }
    public function delete_user(UserDetailsRequest $request)
    {
        try
        {
            $user = User::where('id', $request->id)->first();
            if (!empty($user))
            {
                DB::beginTransaction();
                $user->delete();
                DB::commit();
                return $this->success('User details deleted successfully', [], 200);
            }
            else
            {
                return $this->success('user is already deleted+', []);
            }
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }
}
