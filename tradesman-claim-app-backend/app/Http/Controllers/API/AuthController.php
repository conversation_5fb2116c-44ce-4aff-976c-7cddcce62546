<?php

namespace App\Http\Controllers\API;

use App\Models\User;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\SignInRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Http\Requests\RegisterRequest;
use App\Http\Resources\UserResource;
use App\Services\PermissionService;

class AuthController extends Controller
{
	use ApiResponser;
	/**
	 * Signup API
	 * @param Request $request
	 * @return Json
	 */
	public function __construct()
	{
		//
	}
	public function sign_up(RegisterRequest $request)
	{
		DB::beginTransaction();
		try
		{
			User::create([
				'name' => $request->name,
				'email' => $request->email,
				'password' => Hash::make($request->password),
			]);
			DB::commit();
			return $this->success('User created successfully!', []);
		}
		catch (\Throwable $th)
		{
			DB::rollback();
			return $this->error('Something went wrong', 400);
		}
	}

	/**
	 * Sign-in API
	 *
	 * @return Json
	 */
	public function login(SignInRequest $request, PermissionService $permissionService)
	{
		try
		{

			$user = User::where('email', $request->email)->first();

			if (!$user || !Hash::check($request->password, $user->password))
			{
				return $this->error('incorrect password', 200);
			}

			$user = User::with(
				[
					'user_company','user_role.role_permissions' => function ($query)
					{
						$query->select('id', 'role_type_id', 'form_column_name', 'permission_type');
					}
				]
			)->where('email', $request->email)->first();
			$user['permissions'] = $permissionService->user_permissions($user);
			$user['token'] = $user->createToken('API Token')->plainTextToken;
			return $this->success('User logged-in successfully', UserResource::collection([$user]));
		}
		catch (\Throwable $th)
		{
			return $this->error('Something went wrong', 400);
		}
	}

	// /**
	//  * Logout API
	//  *
	//  * @return json
	//  */
	public function logout(Request $request)
	{
		try
		{
			if ($request->user())
			{
				// Revoke the token that was used to authenticate the current request...
				$request->user()->currentAccessToken()->delete();
			}
			else
			{
				return $this->error('Invalid token', 200);
			}

			return $this->success('User logged-out successfully', []);
		}
		catch (\Throwable $th)
		{
			return $this->error('Something went wrong', 400);
		}
	}
}
