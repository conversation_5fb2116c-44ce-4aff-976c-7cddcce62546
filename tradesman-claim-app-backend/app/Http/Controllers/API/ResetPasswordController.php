<?php

namespace App\Http\Controllers\API;

use Carbon\Carbon;
use App\Models\User;
use Illuminate\Support\Str;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Models\ResetPassword;
use App\Mail\ResetPasswordMail;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Http\Requests\ResetPasswordRequest;
use App\Http\Requests\ForgotPasswordRequest;
use Illuminate\Contracts\Encryption\DecryptException;

class ResetPasswordController extends Controller
{
    use ApiResponser;
    /**
     * Signup API
     * @param Request $request
     * @return Json
     **/
    public function forgot_password(ForgotPasswordRequest $request)
    {
        try
        {
            $email = $request->email;
            $reset_password_data = ResetPassword::where('email', $email)->first();

            if (!empty($reset_password_data))
            {
                ResetPassword::where('email', $email)->delete();
            }

            $random_token = encrypt($email);
            $token_url = env('FRONTEND_APP_URL') . '/' . 'resetpassword/' . $random_token;
            ResetPassword::create([
                'email' => $email,
                'token' => $random_token,
                'created_at' => Carbon::now()
            ]);
            $user_name = User::select('name')->where('email', $email)->first();
            $response = [
                'token_url' => $token_url,
                'user_name' => $user_name->name,
            ];
            try
            {
                Mail::to($email)->send(new ResetPasswordMail($response));
            }
            catch (\Exception $mail_exception)
            {
                Log::error('Error sending reset password email: ', ['error' => $mail_exception->getMessage()]);
                return $this->error('Failed to send reset password email', 400);
            }

            return $this->success('We sent a password reset link to your email. Please check your inbox and spam folders.', []);
        }
        catch (\Throwable $th)
        {
            Log::error('Error resetting password: ', ['error' => $th->getMessage()]);
            return $this->error('Something went wrong', 400);
        }
    }
    public function reset(ResetPasswordRequest $request)
    {
        try
        {
            try
            {
                $email = decrypt($request->token);
            }
            catch (DecryptException $e)
            {
                return $this->error('Invalid token', 400);
            }
            $password = $request->password;
            $user_data = ResetPassword::where('token', $request->token)->where('email', $email)->first();
            if (!$user_data)
            {
                return $this->error('Invalid token', 400);
            }

            if (Carbon::parse($user_data->created_at)->addMinutes(60)->isPast())
            {
                return $this->error('Token has expired', 400);
            }
            $user = User::where('email', $email)->first();
            if (!$user)
            {
                return $this->error('User not found', 400);
            }
            $user->password = Hash::make($password);
            $user->save();
            ResetPassword::where('email', $email)->delete();

            return $this->success('Password updated successfully');
        }
        catch (\Throwable $th)
        {
            Log::error('Error resetting password: ', ['error' => $th->getMessage()]);
            return $this->error('Something went wrong', 400);
        }
    }
}
