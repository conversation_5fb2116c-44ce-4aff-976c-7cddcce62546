<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class ClaimAppDataResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $array = parent::toArray($request);
        unset($array['claims_firm']);
        return array_merge($array, [
            'law_firm_name' => $this->whenLoaded('claims_firm', fn() => $this->claims_firm->title ?? null),
        ]);
    }
}
