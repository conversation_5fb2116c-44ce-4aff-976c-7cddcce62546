<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'token' => $this->whenNotNull($this->token),
            'user_id' => $this->id,
            'company_id' => $this->whenLoaded('user_company', fn() => $this->user_company->id),
            'company_name' => $this->whenLoaded('user_company', fn() => $this->user_company->title),
            'user_name' => $this->name,
            'user_role' => $this->user_role->role_name ?? null,
            'user_permissions' => $this->permissions ?? null,
        ];
    }
}
