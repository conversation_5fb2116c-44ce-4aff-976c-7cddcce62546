<?php

namespace App\Services;

class PermissionService
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
    }

    public function user_permissions($user)
    {
        return $user->user_role?->role_permissions->groupBy('form_column_name')->map(function ($permissions)
        {
            return [
                'field_name' => $permissions->first()->form_column_name,
                'is_editable' => $permissions->contains('permission_type', 'Edit') ? 'Y' : 'N',
                'is_viewable' => $permissions->contains('permission_type', 'View') ? 'Y' : 'N',
            ];
        })->values()->toArray();
    }

    public function edit_able_columns($user)
    {
        return $user->user_role?->role_permissions
            ->groupBy('form_column_name')
            ->filter(function ($permissions)
            {
                return $permissions->contains('permission_type', 'Edit');
            })
            ->keys()
            ->toArray();
    }
}
