<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RoleType extends Model
{
    protected $table='role_type';
    use HasFactory;
    protected $fillable=[
        'role_name',
        'created_at',
        'updated_at',
    ]; 
    public function users_data():HasMany{
        return $this->hasMany(User::class,'id','role_type_id');
    }
    public function role_permissions():HasMany{
        return $this->hasMany(ClaimFormRolePermission::class);
    }

     /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
       
    ];
}
