<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class LawFirm extends Model
{
    use HasFactory, SoftDeletes;
    protected $table = 'law_firm';

    protected $fillable = [
        'title',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function company_users(): HasMany
    {
        return $this->hasMany(User::class, 'law_firm_id', 'id');
    }
}
