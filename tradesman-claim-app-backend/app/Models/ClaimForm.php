<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClaimForm extends Model
{
    use HasFactory;
    protected $table = "claim_form_data";
    protected $fillable = [
        'law_firm_id',
        'broker_id',
        'broker_name',
        'case_analysis',
        'case_fact',
        'case_name',
        'case_concern_to_sm',
        'ctt_or_cts',
        'claim_adjuster',
        'claim_number',
        'claim_status',
        'claim_type',
        'claimant_name',
        'date_of_loss',
        'date_of_loss_notes',
        'effective_date',
        'expense_incurred',
        'expense_outstanding',
        'expense_paid',
        'firm_matter_number',
        'indemnity_incurred',
        'indemnity_outstanding',
        'indemnity_paid',
        'injury_loss',
        'insured_defendant_name',
        'litigation_phase',
        'loss_description',
        'medical_provider',
        'plaintiff_attorny',
        'plaintiff_law_firm',
        'policy_number',
        'property_damage_incurred',
        'property_damage_outstanding',
        'property_damage_paid',
        'reverse_increase_requested',
        'surgeon',
        'tradesman_comments',
        'trail_date',
        'trail_information',
        'venue',
        'defense_counsel',
        'accident_state',
        'expiration_date',
        'work_comp_lien',
        'total_amount_medicals',
        'other_demages',
        'layer_aditional_comments',
        'settlement_value',
        'verdict_value',
        'propose_reserve_increase',
        '1st_chair',
        '2nd_chair',
        'potential_case',
        'industrial_code_voilation_alleged',
        'industrial_code_voilation_opinion'
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function claims_firm():BelongsTo{
        return $this->belongsTo(LawFirm::class,'law_firm_id','id');
    }
}
