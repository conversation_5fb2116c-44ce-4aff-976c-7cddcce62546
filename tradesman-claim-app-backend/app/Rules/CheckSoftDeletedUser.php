<?php

namespace App\Rules;

use Closure;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckSoftDeletedUser implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (User::onlyTrashed()->where('email', $value)->exists()) {
            $fail('Your account is disabled by administrator, please contact admin.');
            return;
        }

        if (User::where('email', $value)->exists()) {
            $fail('Oops! This email address is already registered, Use Sign in button to login.');
        }
    }
}
