<!DOCTYPE html>
<html>

<head>
    <title>Reset Password</title>
    <style>
        body {
            font-family: Arial, Helvetica, sans-serif;
        }

        .card-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f5f5f5;
        }

        .card-main {
            display: flex;
            width: 80%;
            max-width: 800px;
            background-color: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .card-img {
            flex-basis: 40%;
        }

        .card-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .card-content {
            flex-basis: 60%;
            padding: 40px;
        }

        .card-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            word-break: break-all;
        }

        .card-description {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 30px;
            word-break: break-all;
        }

        .card-date {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #888;
            word-break: break-all;
        }

        .date {
            margin: 0;
            word-break: break-all;
        }

        .button {
            background-color: #1a73e8;
            border: none;
            padding: 8px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            -webkit-transition-duration: 0.4s;
            transition-duration: 0.4s;
            border-radius: 5px;
            color: #fff !important;
        }

        .btn-primary:hover {
            box-shadow: 0 12px 16px 0 rgba(0, 0, 0, 0.24), 0 17px 50px 0 rgba(0, 0, 0, 0.19);
        }
    </style>
</head>

<body>
    <section class="container">
        <div class="card-content">
            <p class="card-description">
                Dear {{$user_name}},
                <br><br>
                We received a request to reset the password for your account.
                <br>
                To reset your password, click the button below. The reset password link is valid for the next 1 hour:
                <br>
                <a href="{{ $token_url }}" style="background-color: #1a73e8;
            border: none;
            padding: 8px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            -webkit-transition-duration: 0.4s;
            transition-duration: 0.4s;
            border-radius: 5px;
            color: #fff !important;" >Reset Password</a>
                <br><br>
                Alternatively, you can also use the link below to change your password:
                <br><br>
                <a href="{{ $token_url }}" style="" class="date">{{ $token_url }}</a>
                <br><br>
                Best Regards,
                <br>
                The Tradesman Program
            </p>

        </div>

</body>

</html>