#!/bin/bash
cd /home/<USER>/tradesman-claim-app-backend

sudo cp -R /home/<USER>/tradesman-claim-app-backend  /var/www/
sudo chown -R www-data:www-data /var/www/tradesman-claim-app-backend
sudo chmod -R 755 /var/www/tradesman-claim-app-backend
sudo chown -R ubuntu:ubuntu /var/www/tradesman-claim-app-backend
sudo chmod 777 -R /var/www/tradesman-claim-app-backend/storage/

cd /var/www/tradesman-claim-app-backend
# Install PHP dependencies
composer update --no-dev
composer install --no-dev --optimize-autoloader
sudo npm install
sudo npm run build

rm -rf vendor/phpunit

php artisan key:generate
# php artisan config:cache
# php artisan route:cache
# php artisan view:cache