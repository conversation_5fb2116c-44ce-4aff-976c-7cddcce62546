<?php

namespace Database\Seeders;

use App\Models\LawFirm;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AddLawFirmSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $lawFirms = [
            'Ahmuty Demers & McManus',
            'Barker Patterson & Nichols, LLP',
            'Barry McTiernan & Moore, LLC',
            'Bartlett LLP',
            'Baxter Smith & Shapiro, PC - Hicksville',
            'Black Marjieh & Sanford, LLP',
            'Brody Law Group, PLLC',
            'Catalano Gallardo & Petropoulos, LLP',
            'Cerussi & Spring, P.C.',
            'Chartwell Law',
            '<PERSON>, LLP',
            'Clyde & Co.',
            '<PERSON><PERSON><PERSON>, LLP',
            '<PERSON>, <PERSON>',
            '<PERSON>, LLP',
            '<PERSON><PERSON>, PLLC',
            '<PERSON>, LLP',
            'DeCicco Gibbons & McNamara, PC – Oakland Gardens',
            'DOPF PC',
            '<PERSON><PERSON> & <PERSON>, LLP',
            '<PERSON>, LLP',
            '<PERSON><PERSON> & <PERSON>, LLP',
            'Fi<PERSON>tti & Pesce, LLP',
            'Fleischner Potash',
            'Freeman Mathis & Gary, LLP',
            'Fuchs <PERSON>g, PLLC',
            'Fullerton Beck, LLP',
            'Gallo Vitucci Klar, LLP',
            'Gartner & Bloom, PC',
            'Gerber Ciano Kelly Brady, LLP',
            'Goetz Schenker Blee & Wiederhorn, LLP – New York',
            'Goldberg Segalla',
            'Golden Rothschild Spagnola Lundell Boylan Garubo & Bell',
            'Hannum Feretic Prendergast & Merlino, LLC',
            'Hinshaw & Culbertson, LLP – Greg Lahr',
            'Hurwitz & Fine PC',
            'Jones Jones LLC',
            'Kahana & Feld, LLP',
            'Kaufman Borgeest & Ryan, LLP',
            'Keane & Beane, P.C.',
            'Kelley Kronenberg',
            'Kennedy & Souza, APC – San Diego',
            'Kennedys Law LLP',
            'Kenney Shelton Liptak Nowak, LLP',
            'Kerley Walsh Matera & Cinquemani, P.C.',
            'Kiernan Trebach, LLP',
            'Law Offices of Michael E. Pressman',
            'Lawrence Worden Rainis & Bard, PC - Melville',
            'Lester Schwab Katz & Dwyer, LLP – New York',
            'Levy Sibley Foreman and Speir, LLC - Albany',
            'Lewis Brisbois Bisgaard & Smith, LLP',
            'Lewis Johs Avallone Aviles, LLP',
            'Litchfield Cavo LLP',
            'London Fischer, LLP',
            'Malapero Prisco & Klauber, LLP – New York',
            'Maroney O’Connor, LLP',
            'Marshall Conway Bradley & Gollub, P.C.',
            'Marshall Dennehey',
            'McElroy Deutsch Mulvaney & Carpenter, LLP',
            'McMahon Martine & Gallagher, LLP',
            'Milber Makris Plousadis & Seiden, LLP',
            'Miller Leiby & Associates, P.C. – New York',
            'Mintzer Sarowitz Zeris & Willis, LLP',
            'Montfort Healy McGuire & Salley, LLP',
            'Morris Duffy Alonso & Faley, LLP',
            'Mound Cotton Wolen & Greengrass, LLP',
            'Newman Meyers Kreines Harris, PC',
            'Nicoletti Spinner Ryan Gulino Pinter, LLP (Edward Benson)',
            'Nicoletti Hornig & Sweeney – New York',
            'Novara Tesija Catenacci McDonald & Baas, PLLC - Troy',
            'O’Connor Redd Orlando, LLP',
            'Peirce & Salvato, PLLC',
            'Pillinger Miller Tarallo, LLP',
            'Quintairos, Prieto, Wood & Boyer, P.A.',
            'Raven & Koble, LLP',
            'Rebore Thorpe Pisarello, PC',
            'Resnick & Louis, PC',
            'Ropers Majeski',
            'Russo & Gould LLP',
            'Semmes',
            'Siegel, Moreno & Stettler APC',
            'Smith Mazure, P.C.',
            'Stewart Greenblatt Manning & Baez',
            'Sweetbaum & Sweetbaum',
            'The Law Firm of Adam C. Weiss, PLLC',
            'The Willis Law Group',
            'Torino & Bernstein, P.C.',
            'Vecchione, Vecchione & Cano, LLP',
            'Wade Clark Mulcahy, LLP – New York',
            'Weiss, Wexler & Wornow P.C.',
            'Westermann Sheehy Samaan & Gillespie, LLP',
            'Wilson Elser Moskowitz Edelman & Dicker, LLP',
            'Wood Smith Henning & Berman',
        ];

        foreach ($lawFirms as $firm) {
            LawFirm::updateOrInsert(
                ['title' => $firm],
                ['title' => $firm]
            );
        }
    }
}
