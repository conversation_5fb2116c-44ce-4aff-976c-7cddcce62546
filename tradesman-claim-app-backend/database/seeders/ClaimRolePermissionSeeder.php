<?php

namespace Database\Seeders;

use Carbon\Carbon;
use App\Models\RoleType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\ClaimFormRolePermission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class ClaimRolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        ClaimFormRolePermission::truncate();
        $roles = RoleType::all();
        $final_array = [];
        $hidden_for_lawyer = [
            "indemnity_outstanding",
            "indemnity_paid",
            "reverse_increase_requested",
            "tradesman_comments",
            "propose_reserve_increase",
            "indemnity_incurred"
        ];
        $non_editable_for_lawyer = [
            "broker_id",
            "broker_name",
            "case_concern_to_sm",
            "claim_adjuster",
            "claim_number",
            "claim_status",
            "accident_state",
            "claimant_name",
            "date_of_loss",
            "effective_date",
            "expiration_date",
            "expense_incurred",
            "expense_outstanding",
            "expense_paid",
            "indemnity_incurred",
            "indemnity_outstanding",
            "indemnity_paid",
            "insured_defendant_name",
            "loss_description",
            "policy_number",
            "property_damage_incurred",
            "property_damage_outstanding",
            "property_damage_paid",
            "reverse_increase_requested",
            "propose_reserve_increase",
            "tradesman_comments",
            "defense_counsel",
        ];
        $non_editable_for_admin = [
            "broker_id",
            "claim_status",
            "date_of_loss",
            "expense_incurred",
            "expense_outstanding",
            "expense_paid",
            "firm_matter_number",
            "indemnity_incurred",
            "indemnity_outstanding",
            "indemnity_paid",
            "policy_number",
            "property_damage_incurred",
            "property_damage_outstanding",
            "property_damage_paid",
        ];
        $non_editable_for_tradesman = [
            "broker_id",
            "broker_name",
            "claim_adjuster",
            "claim_number",
            "claim_status",
            "accident_state",
            "claimant_name",
            "date_of_loss",
            "effective_date",
            "expiration_date",
            "expense_incurred",
            "expense_outstanding",
            "expense_paid",
            "firm_matter_number",
            "indemnity_incurred",
            "indemnity_outstanding",
            "indemnity_paid",
            "insured_defendant_name",
            "policy_number",
            "property_damage_incurred",
            "property_damage_outstanding",
            "property_damage_paid",
        ];
        $allColumns = DB::connection()->getSchemaBuilder()->getColumnListing('claim_form_data');
        $excludedColumns = ['id', 'created_at', 'updated_at', 'deleted_at'];
        $filteredColumns = array_diff($allColumns, $excludedColumns);
        foreach ($roles as $role)
        {

            foreach ($filteredColumns as $single_column)
            {
                $permission_type = 'View';

                if ($role->role_name == 'Lawyer' && in_array($single_column, $hidden_for_lawyer))
                {
                    continue;
                }
                $final_array[] = [
                    'role_type_id' => $role->id,
                    'form_column_name' => $single_column,
                    'permission_type' => $permission_type,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                    'deleted_at' => null,
                ];
            }
        }
        foreach ($roles as $role)
        {
            foreach ($filteredColumns as $single_column)
            {
                if ($role->role_name == 'Admin' && in_array($single_column, $non_editable_for_admin))
                {
                    continue;
                }
                else if ($role->role_name == 'Lawyer' && in_array($single_column, $non_editable_for_lawyer))
                {
                    continue;
                }
                else if ($role->role_name == 'Tradesman' && in_array($single_column, $non_editable_for_tradesman))
                {
                    continue;
                }
                else
                {
                    $permission_type = 'Edit';
                    $final_array[] = [
                        'role_type_id' => $role->id,
                        'form_column_name' => $single_column,
                        'permission_type' => $permission_type,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                        'deleted_at' => null,
                    ];
                }
            }
        }
        ClaimFormRolePermission::insert($final_array);
    }
}
