<?php

namespace Database\Seeders;

use Carbon\Carbon;
use App\Models\LawFirm;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class UnassignedLawFirmSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $firm = LawFirm::where('id', 0)->first();
        if (!$firm)
        {
            $firm_data = LawFirm::create([
                'title' => 'Unassigned',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            $firm_data->id = 0;
            $firm_data->save();
        }
    }
}
