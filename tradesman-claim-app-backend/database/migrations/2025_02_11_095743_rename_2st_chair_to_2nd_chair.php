<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('claim_form_data', function (Blueprint $table)
        {
            $table->renameColumn('2st_chair', '2nd_chair');
        });
        Schema::table('claim_form_data_staging', function (Blueprint $table)
        {
            $table->renameColumn('2st_chair', '2nd_chair');
        });
    }

    public function down(): void
    {
        
    }
};
