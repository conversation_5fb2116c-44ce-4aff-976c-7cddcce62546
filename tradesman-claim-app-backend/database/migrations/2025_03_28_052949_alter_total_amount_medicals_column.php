<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('claim_form_data', function (Blueprint $table)
        {
            $table->double('total_amount_medicals')->nullable()->change();
        });
        Schema::table('claim_form_data_staging', function (Blueprint $table)
        {
            $table->double('total_amount_medicals')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
