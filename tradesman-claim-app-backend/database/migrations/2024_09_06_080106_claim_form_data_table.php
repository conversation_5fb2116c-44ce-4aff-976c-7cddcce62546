<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('claim_form_data', function (Blueprint $table) {
            $table->id();
            $table->integer('broker_id')->nullable();
            $table->string('broker_name',100)->nullable();
            $table->string('case_analysis',100)->nullable();
            $table->text('case_fact')->nullable();
            $table->string('case_name')->nullable();
            $table->string('case_concern_to_sm')->nullable();
            $table->string('ctt_or_cts')->nullable();
            $table->string('claim_adjuster',100)->nullable();
            $table->string('claim_number')->nullable();
            $table->string('claim_status',5)->nullable();
            $table->string('claim_supervisor',100)->nullable();
            $table->string('claim_type',5)->nullable();
            $table->string('claimant_name')->nullable();
            $table->date('date_of_loss')->nullable();
            $table->text('date_of_loss_notes')->nullable();
            $table->date('effective_date')->nullable();
            $table->float('expense_incurred')->nullable();
            $table->float('expense_outstanding')->nullable();
            $table->float('expense_paid')->nullable();
            $table->string('firm_matter_number')->nullable();
            $table->float('indemnity_incurred')->nullable();
            $table->float('indemnity_outstanding')->nullable();
            $table->float('indemnity_paid')->nullable();
            $table->string('injury_loss',5)->nullable();
            $table->string('insured_defendant_name',100)->nullable();
            $table->string('litigation_phase',100)->nullable();
            $table->text('loss_description')->nullable();
            $table->float('medical_incured')->nullable();
            $table->float('medical_outstanding')->nullable();
            $table->float('medical_paid')->nullable();
            $table->string('medical_provider',5)->nullable();
            $table->float('other_incured')->nullable();
            $table->float('other_outstanding')->nullable();
            $table->float('other_paid')->nullable();
            $table->string('paralegal',100)->nullable();
            $table->string('plaintiff_attorny',100)->nullable();
            $table->string('plaintiff_law_firm',100)->nullable();
            $table->string('plaintiff_name',100)->nullable();
            $table->string('policy_number',100)->nullable();
            $table->string('potential_case_exposure',100)->nullable();
            $table->string('producer_number',100)->nullable();
            $table->float('property_damage_incurred')->nullable();
            $table->float('property_damage_outstanding')->nullable();
            $table->float('property_damage_paid')->nullable();
            $table->char('reverse_increase_requested',1)->nullable();
            $table->string('surgeon',100)->nullable();
            $table->text('tradesman_comments')->nullable();
            $table->date('trail_date')->nullable();
            $table->string('trail_information')->nullable();
            $table->string('venue',100)->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('claim_form_data');
    }
};
