<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('claim_form_role_permission', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('role_type_id'); 
            $table->string('form_column_name',100);
            $table->enum('permission_type', ['Edit', 'View']);
            $table->dateTime('deleted_at')->nullable();
            $table->foreign('role_type_id')->references('id')->on('role_type')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('claim_form_role_permission', function (Blueprint $table) {
            $table->dropForeign(['role_type_id']);
        });
    
        Schema::dropIfExists('claim_form_role_permission');
    }
    
};
