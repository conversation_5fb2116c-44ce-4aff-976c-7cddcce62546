<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('claim_form_data', function (Blueprint $table)
        {
            $table->dropColumn([
                'other_paid',
                'medical_outstanding',
                'medical_incured',
                'medical_paid',
                'claim_supervisor',
                'other_outstanding',
                'other_incured',
                'paralegal',
                'plaintiff_name',
                'producer_number',
                'potential_case_exposure'
            ]);
            $table->string('accident_state')->after('defense_counsel')->nullable();
            $table->date('expiration_date')->after('accident_state')->nullable();
            $table->string('work_comp_lien')->after('expiration_date')->nullable();
            $table->float('total_amount_medicals')->after('work_comp_lien')->default(0);
            $table->string('other_demages')->after('total_amount_medicals')->nullable();
            $table->string('layer_aditional_comments')->after('other_demages')->nullable();
            $table->string('settlement_value')->after('layer_aditional_comments')->nullable();
            $table->string('verdict_value')->after('settlement_value')->nullable();
            $table->string('propose_reserve_increase')->after('verdict_value')->nullable();
            $table->string('1st_chair')->after('propose_reserve_increase')->nullable();
            $table->string('2st_chair')->after('1st_chair')->nullable();
            $table->string('potential_case', 3)->after('2st_chair')->default('No');
            $table->string('industrial_code_voilation_alleged')->after('potential_case')->nullable();
            $table->string('industrial_code_voilation_opinion')->after('industrial_code_voilation_alleged')->nullable();
        });
        Schema::table('claim_form_data_staging', function (Blueprint $table)
        {
            $table->dropColumn([
                'other_paid',
                'medical_outstanding',
                'medical_incured',
                'medical_paid',
                'claim_supervisor',
                'other_outstanding',
                'other_incured',
                'paralegal',
                'plaintiff_name',
                'producer_number',
                'potential_case_exposure'
            ]);
            $table->string('accident_state')->after('defense_counsel')->nullable();
            $table->date('expiration_date')->after('accident_state')->nullable();
            $table->string('work_comp_lien')->after('expiration_date')->nullable();
            $table->float('total_amount_medicals')->after('work_comp_lien')->default(0);
            $table->string('other_demages')->after('total_amount_medicals')->nullable();
            $table->string('layer_aditional_comments')->after('other_demages')->nullable();
            $table->string('settlement_value')->after('layer_aditional_comments')->nullable();
            $table->string('verdict_value')->after('settlement_value')->nullable();
            $table->string('propose_reserve_increase')->after('verdict_value')->nullable();
            $table->string('1st_chair')->after('propose_reserve_increase')->nullable();
            $table->string('2st_chair')->after('1st_chair')->nullable();
            $table->string('potential_case', 3)->after('2st_chair')->default('No');
            $table->string('industrial_code_voilation_alleged')->after('potential_case')->nullable();
            $table->string('industrial_code_voilation_opinion')->after('industrial_code_voilation_alleged')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
