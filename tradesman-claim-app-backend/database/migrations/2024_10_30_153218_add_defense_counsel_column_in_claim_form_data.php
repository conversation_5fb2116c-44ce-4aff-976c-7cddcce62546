<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('claim_form_data', 'defense_counsel'))
        {
            Schema::table('claim_form_data', function (Blueprint $table)
            {
                $table->text('defense_counsel',100)->nullable()->after('venue');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
