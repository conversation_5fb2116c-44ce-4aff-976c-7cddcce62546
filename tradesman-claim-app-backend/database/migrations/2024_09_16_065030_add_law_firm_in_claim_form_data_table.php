<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('claim_form_data', function (Blueprint $table) {
            $table->unsignedBigInteger('law_firm_id')->nullable()->after('id');
            $table->foreign('law_firm_id')->references('id')->on('law_firm')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('claim_form_data', function (Blueprint $table) {
            $table->dropForeign('claim_form_data_law_firm_id_foreign');  
            $table->dropColumn('law_firm_id');
        });
    }
};
