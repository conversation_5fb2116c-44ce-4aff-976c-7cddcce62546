<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\ClaimFormRolePermissionController;
use App\Http\Controllers\API\UserController;
use App\Http\Controllers\API\ResetPasswordController;
use App\Http\Controllers\API\CompanyController;
use App\Http\Middleware\AdminMiddleware;

Route::post('auth/signup', [AuthController::class, 'sign_up']);
Route::post('auth/signin', [AuthController::class, 'login']);
Route::post('auth/forgotPassword', [ResetPasswordController::class, 'forgot_password']);
Route::post('auth/resetPassword', [ResetPasswordController::class, 'reset']);
Route::group(['middleware' => ['auth:sanctum']], function ()
{
    Route::middleware([AdminMiddleware::class])->group(function ()
    {
        Route::post('/usersListing', [UserController::class, 'index']);
        Route::post('/userDetails', [UserController::class, 'get_user']);
        Route::post('/updateUserDetails', [UserController::class, 'update_user']);
        Route::post('/deleteUser', [UserController::class, 'delete_user']);
        Route::post('/rolesListing', [UserController::class, 'role_listing']);
        Route::post('/createCompany', [CompanyController::class, 'create']);
        Route::post('/companyDetails', [CompanyController::class, 'company_details']);
        Route::post('/updateCompanyDetails', [CompanyController::class, 'update_company']);
        Route::post('/deleteCompany', [CompanyController::class, 'delete_comapny']);
        Route::post('/unassignClaims', [ClaimFormRolePermissionController::class, 'unassignClaims']);
        Route::post('/assignClaimsData', [ClaimFormRolePermissionController::class, 'assignClaimsData']);
    });
    Route::post('/companiesListing', [CompanyController::class, 'index']);
    Route::post('/claimFormsData', [ClaimFormRolePermissionController::class, 'index']);
    Route::post('/claimFormDetails', [ClaimFormRolePermissionController::class, 'edit']);
    Route::post('/updateClaimFormDetails', [ClaimFormRolePermissionController::class, 'update']);
    Route::post('/logout', [AuthController::class, 'logout']);
});
Route::fallback(function ()
{
    return response()->json([
        'status' => 'Error',
        'message' => '404 Not Found',
        'data' => ''
    ], 404);
});
