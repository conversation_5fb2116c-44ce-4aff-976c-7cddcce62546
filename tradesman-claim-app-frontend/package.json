{"name": "tradesman-claim-app-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@mui/icons-material": "^5.16.7", "@mui/material": "^5.16.7", "@mui/styles": "^5.16.7", "@mui/x-date-pickers": "^7.13.0", "@reduxjs/toolkit": "^2.0.1", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^1.54.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.3.4", "bootstrap": "^5.2.3", "chart.js": "^4.2.1", "chartjs-plugin-datalabels": "^2.2.0", "date-object": "^2.1.6", "faker": "^6.6.6", "formik": "^2.2.9", "js-cookie": "^3.0.5", "material-react-table": "^2.13.1", "moment": "^2.29.4", "react": "^18.2.0", "react-bootstrap": "^2.7.2", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-icons": "^4.8.0", "react-multi-date-picker": "^4.4.1", "react-otp-input": "^3.0.1", "react-redux": "^9.1.0", "react-responsive": "^9.0.2", "react-router": "^6.10.0", "react-router-dom": "^6.10.0", "react-scripts": "^5.0.1", "redux-persist": "^6.0.0", "uuid": "^9.0.1", "web-vitals": "^2.1.4", "yup": "^1.0.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "plugins": ["react-hooks"], "rules": {"react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "off"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11"}}