import { makeStyles } from '@mui/styles';
import { createTheme } from '@mui/material/styles';

const theme = createTheme({
    palette: {
        primary: {
            main: '#099250',
        },
        secondary: {
            main: '#ff9b00',
        },
    },
});

const useStyles = makeStyles({
    table: {
        minWidth: 650,
        borderCollapse: 'separate !important',
        maxWidth: '100%',
    },
    headerCell: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        borderBottom: '1px solid #e0e0e0',
    },
    dataCell: {
        borderBottom: '10px solid #e0e0e0',
    },
    // row: {
    //     display: 'flex !important',
    //     flexDirection: 'row !important',
    //     width: '100%',

    //     backgroundColor: '#fff',
    //     borderRadius: '12px',
    //     boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
    //     marginBottom: '16px',
    //     overflow: 'hidden',
    //     '& > td': {
    //         flex: '1',
    //         display: 'flex',
    //         alignItems: 'center',
    //         justifyContent: 'center',
    //         padding: '16px',
    //         whiteSpace: 'nowrap',
    //         textOverflow: 'ellipsis',
    //         overflow: 'hidden',
    //         borderBottom: 'none',
    //         '& > div': {
    //             width: '100%',
    //             display: 'flex',
    //             alignItems: 'center',
    //             justifyContent: 'space-between',
    //         },
    //     },
    // },
    statusCell: {
        width: '10%',
    },
    claimNumberCell: {
        width: '10%',
    },
    amountCell: {
        width: '10%',
        textAlign: 'right',
    },
});

export { useStyles, theme };
