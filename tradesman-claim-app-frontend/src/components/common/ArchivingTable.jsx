import React, { useEffect, useState } from "react";
import "./style.css";
import ArrowLeft from "./../../assets/images/arrow--left.svg";
import ArrowRight from "./../../assets/images/arrow--right.svg";
import DownArrow from "./../../assets/images/chevron--sort--down.svg";
import UpArrow from "./../../assets/images/chevron--sort.svg";
import ArrowRightVisible from "./../../assets/images/arrow--right-black.svg";
import ArrowleftVisible from "./../../assets/images/arrow-left-black.svg";
import ProgressBar from "../common/Progress";
import { useNavigate } from "react-router";
import { colors } from "../../constants/colors";

const itemsPerPage = 3;
const ArchivingTable = ({
  name,
  totalRows,
  totalData,
  heading,
  data,
  serverName,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [sortedData, setSortedData] = useState(data);
  const [sortOrder, setSortOrder] = useState({
    database_name: false,
    total_tables_archived: false,
    total_storage_archived: false,
  });
  const navigate = useNavigate();
  const lastPageIndex = Math.ceil(sortedData?.length / itemsPerPage);

  const sortData = (column) => {
    const newData = [...sortedData]?.sort((a, b) => {
      switch (column) {
        case "database_name":
          return sortOrder?.database_name
            ? a[column].localeCompare(b[column])
            : b[column].localeCompare(a[column]);
        case "total_tables_archived":
          return sortOrder?.total_tables_archived
            ? parseInt(b.total_tables_archived) -
                parseInt(a.total_tables_archived)
            : parseInt(a.total_tables_archived) -
                parseInt(b.total_tables_archived);
        case "total_storage_archived":
          return sortOrder?.total_storage_archived
            ? parseFloat(b.total_storage_archived) -
                parseFloat(a.total_storage_archived)
            : parseFloat(a.total_storage_archived) -
                parseFloat(b.total_storage_archived);
        default:
          return 0;
      }
    });

    setSortedData(newData);
  };

  const renderData = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return sortedData?.slice(startIndex, endIndex);
  };

  const handlePrevious = () => {
    setCurrentPage((prevPage) => Math.max(1, prevPage - 1));
  };

  const handleNext = () => {
    setCurrentPage((prevPage) => Math.min(lastPageIndex, prevPage + 1));
  };

  const handleSort = (column) => {
    if (!Array.isArray(sortedData)) {
      return;
    }
    sortData(column);
    setSortOrder((prevSortOrder) => ({
      ...Object.keys(prevSortOrder).reduce((acc, key) => {
        acc[key] = key === column ? !prevSortOrder[key] : false;
        return acc;
      }, {}),
    }));
  };

  useEffect(() => {
    setSortedData(data);
  }, [data]);
  return (
    <>
      <div className="server-container-chart server-table-bg">
        <div className="archiving-table-header">{heading}</div>
        <div style={{ overflowX: "auto" }}>
          <table className="table-container" style={{ overflowX: "scroll" }}>
            <tbody>
              <tr className="header-container-border">
                <th style={{ paddingLeft: "24px", width: "50%" }}>
                  <div className="header-container server-name-table">
                    <span className="">
                      {name}
                      <img
                        className="sorting-table-icons"
                        onClick={() => handleSort("database_name")}
                        src={sortOrder?.database_name ? UpArrow : DownArrow}
                        alt="sort"
                      />
                    </span>
                    <span className="border-table-server"></span>
                  </div>
                </th>
                <th style={{ minWidth: "180px", width: "234px" }} className="">
                  <div className="header-container server-table-heading">
                    <span className="server-table-heading">
                      <span>{totalRows}</span>
                      <img
                        className="sorting-table-icons"
                        onClick={() => handleSort("total_tables_archived")}
                        src={
                          sortOrder?.total_tables_archived ? UpArrow : DownArrow
                        }
                        alt=""
                      />
                    </span>
                    <span className="border-table"></span>
                  </div>
                </th>
                <th style={{ minWidth: "412px" }}>
                  <div className="header-container server-table-heading">
                    <span className="server-table-heading">
                      <div>{totalData}</div>
                      <img
                        className="sorting-table-icons"
                        onClick={() => handleSort("total_storage_archived")}
                        src={
                          sortOrder?.total_storage_archived
                            ? UpArrow
                            : DownArrow
                        }
                        alt=""
                      />{" "}
                    </span>
                  </div>
                </th>
              </tr>
              {renderData()?.length === 0 ? (
                <tr style={{ height: "184px" }}>
                  <td colSpan="8">
                    <div className="no-atabase-selected-container">
                      <div className="no-atabase-selected-container-text">
                        <div>No database archiving activities available</div>{" "}
                      </div>
                    </div>
                  </td>
                </tr>
              ) : (
                renderData()?.map((server, index) => {
                  return (
                    <tr key={index} className="table-rows">
                      <td style={{ paddingLeft: "24px" }}>
                        {" "}
                        <div
                          onClick={() =>
                            navigate("/database", {
                              state: {
                                id: server?.id,
                                serverId: server?.server_id,
                                name: server?.database_name,
                                serverName: server?.server_name
                                  ? server?.server_name
                                  : serverName,
                              },
                            })
                          }
                          className="table-rows-link"
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "8px",
                          }}
                        >
                          {server?.database_name}
                        </div>
                      </td>
                      <td className="table-rows-color">
                        <span>{server?.total_tables_archived}</span>
                      </td>
                      <td>
                        {" "}
                        <div
                          style={{
                            display: "flex",
                            gap: "8px",
                            alignItems: "center",
                          }}
                        >
                          <div className="table-progress-container">
                            <span
                              style={{ minWidth: "68px", textAlign: "right" }}
                            >
                              {server?.total_storage_archived} MB
                            </span>
                            <ProgressBar
                              key={index}
                              bgcolor={colors[index]}
                              completed={
                                server?.total_storage_archived_percentage
                              }
                            />
                          </div>
                          <span>
                            {server?.total_storage_archived_percentage?.toFixed(
                              2
                            )}
                            %
                          </span>
                        </div>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
        <div className="archiving-table-footer">
          <button
            className={`archiving-table-footer-button ${
              currentPage > 1 ? "active" : ""
            }`}
            onClick={handlePrevious}
            disabled={currentPage === 1}
          >
            {" "}
            <img
              className="btns-arrow"
              alt=">"
              src={currentPage > 1 ? ArrowleftVisible : ArrowLeft}
            />
            <span>Previous</span>{" "}
          </button>
          <span className="table-count">{currentPage}</span>
          <button
            className={`archiving-table-footer-button ${
              data?.length > 3
                ? currentPage !== lastPageIndex
                  ? "active"
                  : ""
                : ""
            }`}
            onClick={handleNext}
            disabled={currentPage === lastPageIndex}
          >
            {" "}
            <span>Next</span>{" "}
            <img
              className="btns-arrow"
              alt=">"
              src={
                data?.length > 3
                  ? currentPage !== lastPageIndex
                    ? ArrowRightVisible
                    : ArrowRight
                  : ArrowRight
              }
            />{" "}
          </button>
        </div>
      </div>
    </>
  );
};

export default ArchivingTable;
