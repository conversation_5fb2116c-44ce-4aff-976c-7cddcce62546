import React, { useEffect } from "react";
import "./style.css";

const ToastMessage = ({ message, onUndo, isVisibleToast, undoState }) => {

  const handleUndo = () => {
    onUndo();
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      undoState();
    }, 5000);

    return () => clearTimeout(timer);
  }, []);


 

  return (
    <div className={`toast-message-undo ${isVisibleToast ? "visible" : ""}`}>
      <div className="toast-content">{message}</div>
      <div className="undo-button" onClick={handleUndo}>
        UNDO
      </div>
    </div>
  );
};

export default ToastMessage;
