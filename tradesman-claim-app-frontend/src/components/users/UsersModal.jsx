import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, Button, Alert, Row, Col } from "react-bootstrap";
import { useFormik } from "formik";
import { v4 as uuidv4 } from "uuid";
import userValidationSchema from "../../utils/userValidation";
import { handleUserDetails } from "../../services/users";
import { handleCompanyDetails } from "../../services/companies";
import Loader from "../loader/Loader";

import { Select } from "@mui/material";
import { makeStyles } from "@mui/styles";
import { styled } from "@mui/system";
import { MdClose } from "react-icons/md";

const TradesmanSelect = styled(Select)`
  & .MuiSelect-root {
    padding: 10px 16px;
    border-radius: 8px;
    border: 1px solid #eaecf0;
    width: 100%;
    margin: 0px 8px;
    height: 46px;
    &:hover {
      border-color: "red !important";
    }
  }
  & .MuiOutlinedInput-root {
    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      border-width: 1px;
      border-color: #099250;
      box-shadow: 0px 0px 0px 4px #d3f8df;
    }
    & .MuiSelect-icon {
      margin-right: 6px;
      margin-top: -2px;
      width: 20px;
      height: 20px;
    }
    font-family: "Inter";
    font-size: 14px;
    line-height: 22px;
    font-weight: 500;
  }
`;

const useStyles = makeStyles({
  chip: {
    marginTop: "4px !important",
    padding: "2px, 8px, 2px, 8px",
    fontFamily: "Inter !important",
    fontSize: "14px",
    lineHeight: "22px",
    fontWeight: "500 !important",
    textAlign: "center",
  },

  selectRoot: {
    "& .MuiOutlinedInput-root": {
      "&:hover .MuiOutlinedInput-notchedOutline": {
        borderWidth: "1px",
        borderColor: "#099250",
        boxShadow: "0px 0px 0px 4px #D3F8DF",
      },
      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
        borderWidth: "1px",
        borderColor: "#099250",
        boxShadow: "0px 0px 0px 4px #D3F8DF",
      },
      "& .MuiSelect-icon": {
        marginRight: "6px",
        marginTop: "-2px",
        width: "20px",
        height: "20px",
      },
      fontFamily: "Inter",
      fontSize: "14px",
      lineHeight: "22px",
      fontWeight: "500",
    },
  },
});
function UserModal({
  deleteModalStatus,
  editModalStatus,
  handleClose,
  userData,
}) {
  const [open, setOpen] = useState(false);
  const [errorResponse, setErrorResponse] = useState([]);
  const [createUUID, setCreateUUID] = useState("");
  const [roles, setRoles] = useState([]);
  const [companies, setCompanies] = useState([]);

  const [status, setStatus] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        const fetchedRoles = await handleUserDetails.getRoles();
        const fetchedCompanies = await handleCompanyDetails.getCompanies({
          limit: 1000,
        });
        setRoles(fetchedRoles);
        setCompanies(fetchedCompanies.companies_data);
      } catch (err) {
        console.error("Error fetching roles or companies:", err);
      }
    };
    fetchData();
  }, []);

  // Reset the form when the modal status changes
  useEffect(() => {
    resetForm();
    setCreateUUID(uuidv4().replace(/-/g, ""));
    setErrorResponse("");
  }, [editModalStatus, deleteModalStatus]);

  // Initialize Formik Values
  const initialValues = {
    name: userData.name ?? "",
    id: userData.id ?? "",
    role_name: userData.role_name ?? "",
    role_id:
      roles.find((role) => role.role_name == userData.role_name)?.id ?? "",
    company_id: userData.company_id ?? "",
    user_company: userData.user_company ?? "",
  };

  const {
    values,
    handleChange,
    handleBlur,
    handleSubmit,
    errors,
    touched,
    resetForm,
    setFieldValue,
  } = useFormik({
    initialValues,
    validationSchema: userValidationSchema,
    validateOnChange: true,
    validateOnBlur: false,
    enableReinitialize: true,
    onSubmit: async (values) => {
      setErrorResponse("");
      setOpen(true);
      try {
        const payload = {
          ...values,
          role_id: values.role_id,
          company_id: values.company_id,
        };
        const response = await handleUserDetails.updateDetails(payload);
        if (response.status === "Success") {
        } else {
          setErrorResponse(response.messages);
        }
      } catch (error) {
        setErrorResponse(["An error occurred while submitting the claim."]);
      }
      setOpen(false);
      resetForm();
      handleClose();
    },
  });

  const handleClickDelete = async () => {
    try {
      const response = await handleUserDetails.deleteUser({ id: values.id });
      if (response.status == "Success" || response.status == 200) {
        setOpen(false);
        resetForm();
        handleClose();
      } else {
        setOpen(false);
        setErrorResponse(response.messages);
      }
    } catch (error) {
      setErrorResponse(["An error occurred while submitting the claim."]);
    }
  };

  return (
    <>
      <Loader open={open} />
      <Modal
        show={deleteModalStatus}
        onHide={handleClose}
        style={{ marginTop: "15%" }}
      >
        <div className="p-4">
          <h4 className="text-center">Do you want to delete a user?</h4>
          <div className="mt-0">
            <div className="d-flex gap-3 justify-content-center">
              <div className="d-flex w-50">
                <Button className="btn cancel-btn" onClick={handleClose}>
                  Cancel
                </Button>
              </div>
              <div className="d-flex w-50">
                <Button
                  className="btn auth-page-btn"
                  id="auth-page-btn-delete"
                  onClick={handleClickDelete}
                >
                  Delete
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Modal>
      <Modal
        show={editModalStatus}
        onHide={handleClose}
        style={{ marginTop: "25vh" }}
      >
        <div className="p-4">
          <div className="d-flex justify-content-between">
            <h4 className="text-left mb-4">Edit User</h4>
            <div className="d-flex justify-content-end">
              <div className="close-icon-wrapper" onClick={handleClose}>
                <MdClose className="close-icon" />
              </div>
            </div>
          </div>
          {errorResponse !== "" && (
            <Alert
              variant="danger"
              className="mb-0"
              style={{ fontSize: "14px" }}
            >
              {errorResponse &&
                errorResponse.map((res, index) => <div key={index}>{res}</div>)}
            </Alert>
          )}
          <div className="tab-content position-relative">
            <Form
              onSubmit={handleSubmit}
              className="d-block"
              autoComplete="off"
            >
              <div className="pb-2">
                <Row>
                  <Col className="mb-2">
                    <Form.Label className="ui-button-s">User's Name</Form.Label>
                    <Form.Control
                      type="text"
                      placeholder="Name"
                      name="name"
                      value={values.name}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      className={`pwd-field-bg ${errors.name && touched.name ? "auth-page-field-with-error" : null}`}
                    />
                    {errors.name && touched.name && (
                      <div className="auth-page-field-error">{errors.name}</div>
                    )}
                  </Col>
                </Row>
                <Row>
                  <Col className="mb-3">
                    <Form.Label className="ui-button-s">Role</Form.Label>
                    <Form.Select
                      aria-label="Role"
                      value={values.role_id} // Bind to role_id instead of role_name
                      name="role_id" // Make sure this targets the correct field
                      onChange={(e) => {
                        const selectedRoleId = e.target.value;
                        const selectedRole = roles.find(
                          (role) => role.id == selectedRoleId,
                        );
                        setFieldValue("role_id", selectedRoleId); // Set role_id
                        setFieldValue("role_name", selectedRole?.role_name); // Also set role_name for display
                      }}
                      onBlur={handleBlur}
                      title="Role"
                      className={
                        errors.role_name && touched.role_name
                          ? "auth-page-field-with-error"
                          : null
                      }
                    >
                      <option value="">Select Role</option>
                      {roles.map((role, index) => (
                        <option key={index} value={role.id}>
                          {role.role_name}
                        </option>
                      ))}
                    </Form.Select>
                    {errors.role_name && touched.role_name && (
                      <div className="auth-page-field-error">
                        {errors.role_name}
                      </div>
                    )}
                  </Col>
                </Row>
                <Row>
                  <Col className="mb-3">
                    <Form.Label className="ui-button-s mt-2">
                      Law Firm
                    </Form.Label>
                    <Form.Select
                      aria-label="Law Firm"
                      value={values.company_id} // Bind to company_id
                      name="company_id"
                      onChange={(e) => {
                        const selectedCompanyId = e.target.value;
                        const selectedCompany = companies.find(
                          (company) => company.id == selectedCompanyId,
                        );
                        setFieldValue("company_id", selectedCompanyId); // Set company_id
                        setFieldValue("user_company", selectedCompany?.title); // Also set user_company (company name)
                      }}
                      onBlur={handleBlur}
                      className={
                        errors.user_company && touched.user_company
                          ? "auth-page-field-with-error"
                          : null
                      }
                    >
                      <option value="">Select Law Firm</option>
                      {companies.map((company, index) => (
                        <option key={index} value={company.id}>
                          {company.title}
                        </option>
                      ))}
                    </Form.Select>
                  </Col>
                </Row>
              </div>
              <div className="mt-0">
                <div className="d-flex gap-3 justify-content-center">
                  <div className="d-flex w-50">
                    <Button className="btn cancel-btn" onClick={handleClose}>
                      Cancel
                    </Button>
                  </div>
                  <div className="d-flex w-50">
                    <Button className="btn auth-page-btn" type="submit">
                      Save
                    </Button>
                  </div>
                </div>
              </div>
            </Form>
          </div>
        </div>
      </Modal>
    </>
  );
}

export default UserModal;
