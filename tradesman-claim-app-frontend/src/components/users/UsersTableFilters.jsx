import React, { useState } from 'react';
import Form from 'react-bootstrap/Form';
import { MenuItem, Select, FormControl, InputLabel, Chip } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { FaAngleDown } from "react-icons/fa6";


const useStyles = makeStyles({
    chip: {
        marginTop: '4px !important',
        padding: '2px, 8px, 2px, 8px',
        fontFamily: 'Inter !important',
        fontSize: '14px',
        lineHeight: '22px',
        fontWeight: '500 !important',
        textAlign: 'center',
    },

    selectRoot: {
        '& .MuiOutlinedInput-root': {
            '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: "#099250",
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderWidth: '1px',
                borderColor: "#099250",
                boxShadow: '0px 0px 0px 4px #D3F8DF',
            },
            '& .MuiSelect-icon': {
                marginRight: '6px',
                marginTop: '-2px',
                width: '20px',
                height: '20px',
            },
            fontFamily: 'Inter',
            fontSize: '14px',
            lineHeight: '22px',
            fontWeight: '500',
        },
    },
});

function TableCustomFilters({
    handleFilterSelect,
    selectedFilterValue,
}) {
    const [status, setRole] = useState('');
    const classes = useStyles();

    const handleChange = (event) => {
        setRole(event.target.value);
        handleFilterSelect(event.target.value);
    };

    return (
        <>
            <Form className='d-flex align-items-center filter-gap'>
                <FormControl
                    variant="outlined"
                    style={{
                    }}
                    className={classes.selectRoot}
                >
                    <InputLabel
                        sx={{
                            marginTop: '-4px',
                        }}
                        shrink={false}
                    >
                        {selectedFilterValue ? '' : 'Role'}
                    </InputLabel>
                    <Select
                        IconComponent={FaAngleDown}
                        value={selectedFilterValue}
                        noValidate={false}
                        onChange={handleChange}
                        sx={{
                            borderRadius: '8px',
                            border: '1px solid #EAECF0',
                            width: "140px",
                            padding: "10px 0px",
                            height: "46px",
                            '&:hover': {
                                borderColor: '#099250',
                            },
                        }}
                        MenuProps={{
                            PaperProps: {
                                sx: {
                                    borderRadius: '8px',
                                    boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
                                    '& .MuiMenuItem-root': {
                                        padding: '0 8px',
                                    },
                                    '& .MuiMenuItem-root.Mui-selected': {
                                        backgroundColor: '#FFFFFF',
                                    },
                                    '& .MuiMenuItem-root:hover': {
                                        backgroundColor: '#FFFFFF !important',
                                    },
                                },
                            },
                        }
                        }
                    >
                        <MenuItem value="Lawyer" className={classes.chip} >
                            <Chip label="Lawyer" size="small" style={{ backgroundColor: "#ECFDF3", color: "#6941C6", fontFamily: "Inter", fontWeight: "400" }} />
                        </MenuItem>
                        <MenuItem value="Tradesman" className={classes.chip} >
                            <Chip label="Tradesman" size="small" style={{ backgroundColor: "#EFF8FF", color: "#175CD3", fontFamily: "Inter", fontWeight: "400" }} />
                        </MenuItem>
                    </Select>
                </FormControl>
            </Form >
        </>
    );
}

export default TableCustomFilters;

