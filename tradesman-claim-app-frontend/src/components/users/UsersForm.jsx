import React from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Form, Button, Row, Col, InputGroup } from "react-bootstrap";

const validationSchema = Yup.object().shape({
  insuredCompanyName: Yup.string().required("Insured Company Name is required"),
  insuredOfficeAddress: Yup.string().required(
    "Insured Office Address is required",
  ),
  streetAddressLine2: Yup.string(),
  city: Yup.string().required("City is required"),
  state: Yup.string().required("State is required"),
  zipCode: Yup.string().required("Zip Code is required"),
  country: Yup.string().required("Country is required"),
  ownerOrRental: Yup.string().required("Owner or Rental is required"),
  damageReason: Yup.string().required("Damage Reason is required"),
  claimStatus: Yup.string().required("Claim Status is required"),
});

function UsersForm({ initialValues, claimNumber }) {
  const { values, handleChange, handleBlur, handleSubmit, errors, touched } =
    useFormik({
      initialValues: {
        insuredCompanyName: "",
        insuredOfficeAddress: "",
        streetAddressLine2: "",
        city: "",
        state: "California",
        zipCode: "",
        country: "USA",
        ownerOrRental: "",
        damageReason: "Human Error",
        claimStatus: "Reviewed",
        ...initialValues,
      },
      validationSchema,
      onSubmit: (values) => {},
    });

  return (
    <Form onSubmit={handleSubmit} className="w-100">
      <Row className="mb-3">
        <Col>
          <h3>Claim #{claimNumber}</h3>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col>
          <Form.Group>
            <Form.Label>Insured Company Name*</Form.Label>
            <Form.Control
              type="text"
              name="insuredCompanyName"
              value={values.insuredCompanyName}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={
                touched.insuredCompanyName && errors.insuredCompanyName
              }
            />
            <Form.Control.Feedback type="invalid">
              {errors.insuredCompanyName}
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col>
          <Form.Group>
            <Form.Label>Insured Office Address*</Form.Label>
            <Form.Control
              type="text"
              name="insuredOfficeAddress"
              value={values.insuredOfficeAddress}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={
                touched.insuredOfficeAddress && errors.insuredOfficeAddress
              }
            />
            <Form.Control.Feedback type="invalid">
              {errors.insuredOfficeAddress}
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col>
          <Form.Group>
            <Form.Label>Street Address Line 2</Form.Label>
            <Form.Control
              type="text"
              name="streetAddressLine2"
              value={values.streetAddressLine2}
              onChange={handleChange}
              onBlur={handleBlur}
            />
          </Form.Group>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col md={6}>
          <Form.Group>
            <Form.Label>City*</Form.Label>
            <Form.Control
              type="text"
              name="city"
              value={values.city}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={touched.city && errors.city}
            />
            <Form.Control.Feedback type="invalid">
              {errors.city}
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group>
            <Form.Label>State*</Form.Label>
            <Form.Select
              name="state"
              value={values.state}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={touched.state && errors.state}
            >
              <option value="California">California</option>
              {/* Add other states as needed */}
            </Form.Select>
            <Form.Control.Feedback type="invalid">
              {errors.state}
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col md={6}>
          <Form.Group>
            <Form.Label>Zip Code*</Form.Label>
            <Form.Control
              type="text"
              name="zipCode"
              value={values.zipCode}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={touched.zipCode && errors.zipCode}
            />
            <Form.Control.Feedback type="invalid">
              {errors.zipCode}
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group>
            <Form.Label>Country*</Form.Label>
            <Form.Select
              name="country"
              value={values.country}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={touched.country && errors.country}
            >
              <option value="USA">USA</option>
              {/* Add other countries as needed */}
            </Form.Select>
            <Form.Control.Feedback type="invalid">
              {errors.country}
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col>
          <Form.Group>
            <Form.Label>Owner or Rental*</Form.Label>
            <Form.Select
              name="ownerOrRental"
              value={values.ownerOrRental}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={touched.ownerOrRental && errors.ownerOrRental}
            >
              <option value="">Please Select</option>
              <option value="owner">Owner</option>
              <option value="rental">Rental</option>
            </Form.Select>
            <Form.Control.Feedback type="invalid">
              {errors.ownerOrRental}
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col>
          <Form.Group>
            <Form.Label>Damage Reason*</Form.Label>
            <div>
              <Form.Check
                inline
                type="radio"
                label="Natural Disaster"
                name="damageReason"
                value="Natural Disaster"
                checked={values.damageReason === "Natural Disaster"}
                onChange={handleChange}
              />
              <Form.Check
                inline
                type="radio"
                label="Human Error"
                name="damageReason"
                value="Human Error"
                checked={values.damageReason === "Human Error"}
                onChange={handleChange}
              />
              <Form.Check
                inline
                type="radio"
                label="Other"
                name="damageReason"
                value="Other"
                checked={values.damageReason === "Other"}
                onChange={handleChange}
              />
            </div>
          </Form.Group>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col>
          <Form.Group>
            <Form.Label>Claim Status</Form.Label>
            <Form.Select
              name="claimStatus"
              value={values.claimStatus}
              onChange={handleChange}
              onBlur={handleBlur}
            >
              <option value="Reviewed">Reviewed</option>
              {/* Add other status options as needed */}
            </Form.Select>
          </Form.Group>
        </Col>
      </Row>

      <Row>
        <Col className="d-flex justify-content-between">
          <Button variant="secondary" type="button">
            Cancel
          </Button>
          <Button variant="primary" type="submit">
            Save
          </Button>
        </Col>
      </Row>
    </Form>
  );
}

export default UsersForm;
