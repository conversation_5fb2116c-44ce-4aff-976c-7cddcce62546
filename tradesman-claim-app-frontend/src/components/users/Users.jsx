import React, { useState, useMemo, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  useMaterialReactTable,
  MaterialReactTable,
} from "material-react-table";
import { Container, Button } from "react-bootstrap";
import UsersTableFilters from "./UsersTableFilters";
import Loader from "../../components/loader/Loader";
import UsersModal from "./UsersModal";
import "../common/tableStyles.css";
import { LuSearch, LuTrash2, LuPen } from "react-icons/lu";
import { Pagination } from "@mui/material";
import { styled } from "@mui/system";

import { handleUserDetails } from "../../services/users";

import Chip from "@mui/material/Chip";

import { theme, useStyles } from "../common/styles";

function UsersTableWrapper() {
  const [open, setOpen] = useState(false);
  const [usersData, setUsersData] = useState([]);
  const [rowSelection, setRowSelection] = useState([]);
  const [selectedRowArr, setSelectedRowArr] = useState([]);
  const [selectedRowName, setSelectedRowName] = useState([]);
  const [editModalStatus, setEditModalStatus] = useState(false);
  const [deleteModalStatus, setDeleteModalStatus] = useState(false);
  const [responseMessage, setResponseMessage] = useState();
  const [confirmModal, setConfirmModal] = useState(false);
  const [modalInfo, setModalInfo] = useState(false);
  const searchRef = useRef(null);
  const navigate = useNavigate();
  const [selectedFilterValue, setSelectedFilterValue] = useState("");

  const [selectedUser, setSelectedUser] = useState({});

  const StyledPagination = styled(Pagination)({
    className: "myStyledPagination",
    display: "flex",
    position: "relative",
    marginTop: "-40px",
    zIndex: "999",
    justifyContent: "center",
    width: "100%",
    color: "#475467 !important",
    "& .MuiPagination-ul": {
      width: "100%",
      justifyContent: "center",
      padding: "0 16px",
      backgroundColor: "#red",
    },
    "& .MuiPagination-ul li": {
      color: "#475467",
    },
    "& .MuiPagination-ul li:last-child, & .MuiPagination-ul li:first-child": {
      fontWeight: "600 !important",
    },
    "& .MuiPagination-ul li:last-child": {
      marginLeft: "auto",
    },
    "& .MuiPagination-ul li:last-child button::before": {
      content: "'Next'",
      marginRight: "8px",
    },
    "& .MuiPagination-ul li:first-child": {
      marginRight: "auto",
    },
    "& .MuiPagination-ul li:first-child button::after": {
      content: "'Previous'",
      marginLeft: "8px",
    },
    "& .MuiPaginationItem-root": {
      fontFamily: "Inter",
      fontSize: "14px !important",
      lineHeight: "20px",
      fontWeight: "500",
      boxShadow: "none",
    },
    "& .MuiPaginationItem-page": {
      height: "40px",
      width: "40px",
    },
    "& .MuiPaginationItem-page:hover": {
      backgroundColor: "#ECFDF3",
    },
    "& .MuiPaginationItem-page.Mui-selected": {
      backgroundColor: "#FFF",
      color: "#099250",
      border: "1px solid #CDD5DF",
      borderRadius: "8px",
    },
    "& .MuiPaginationItem-ellipsis": {
      paddingTop: "11px",
    },
  });

  const classes = useStyles();

  useEffect(() => {
    getUsersData();
  }, []);

  const handleUserClickEdit = (rowData) => {
    setSelectedUser(rowData);
    setEditModalStatus(true);
    setModalInfo(true);
  };

  const handleUserClickDelete = (rowData) => {
    setSelectedUser(rowData);
    setDeleteModalStatus(true);
    setModalInfo(true);
  };

  const handleClose = () => {
    setSelectedUser({});
    setEditModalStatus(false);
    setDeleteModalStatus(false);
    handleReset();
  };

  useEffect(() => {
    clearSearchField();
  }, [searchRef]);

  // Reset All Filters
  const clearSearchField = () => {
    const inputElement = searchRef.current.querySelector(".MuiInputBase-input");
    // Check if the input element exists before setting its value
    if (inputElement) {
      inputElement.value = "";
      searchRef.current.value = "";
    }
  };

  const handleReset = () => {
    setSelectedFilterValue("");
    clearSearchField();
    setRowSelection([]);
    setSelectedRowArr([]);
    getUsersData();
  };

  const handleFilterSelect = (event) => {
    const selectedValue = event;
    setSelectedFilterValue(selectedValue);
    const newUserData = usersData.filter(
      (user) => user.role_name == selectedValue,
    );
    setUsersData(newUserData);
  };
  const CustomTableToolbar = () => {
    // Custom Table Headers Options
    return (
      <div className="d-flex justify-content-end w-100">
        <div className="table-header search-filter-gap">
          <UsersTableFilters
            handleFilterSelect={handleFilterSelect}
            selectedFilterValue={selectedFilterValue}
          />
        </div>
      </div>
    );
  };

  // Table Headers
  const userColumns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: "User Name",
        size: "auto",
      },
      {
        accessorKey: "role_name",
        header: "Role",
        size: 15,
        Cell: ({ cell }) => {
          const value = cell.getValue();
          return (
            <Chip
              label={value || " - "}
              size="small"
              style={{
                backgroundColor: "#FFFFFF",
                color: value === "Lawyer" ? "#6941C6" : "#175CD3",
                margin: "0px",
              }}
            />
          );
        },
      },
      {
        accessorKey: "user_company",
        company: "user_company",
        header: "Law Firm",
        size: "auto",
        fontSize: "10px",
      },
      {
        accessorKey: "id",
        id: "id",
        size: 5,
        align: "right",
        Cell: ({ cell }) => {
          const rowData = cell.row.original;
          return (
            <>
              <Button
                variant={rowData.role_name === "Admin" ? "link" : ""}
                size="sm"
                onClick={() => handleUserClickDelete(rowData)}
                disabled={rowData.role_name === "Admin"}
              >
                <LuTrash2 />
              </Button>
              <Button
                variant={rowData.role_name === "Admin" ? "link" : ""}
                className="admin-row-btn"
                size="sm"
                onClick={() => handleUserClickEdit(rowData)}
                disabled={rowData.role_name === "Admin"}
              >
                <LuPen />
              </Button>
            </>
          );
        },
      },
    ],
    [],
  );

  // Get Users Data
  const getUsersData = async (selectedValue) => {
    setOpen(true);
    try {
      const response = await handleUserDetails.getUsers();
      const filteredUsers = response.users_data.filter((user) => user.id !== 1);
      setUsersData(filteredUsers);
      setOpen(false);
    } catch (error) {
      console.error("Error fetching users:", error);
      setOpen(false);
      throw error;
    }
  };

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10, //customize the default page size
  });

  // Fix the page change handler
  const handlePageChange = (event, value) => {
    setPagination((prev) => ({
      ...prev,
      pageIndex: value - 1, // Subtract 1 to convert to 0-based index
    }));
  };

  const customTable = useMaterialReactTable({
    columns: userColumns,
    data: usersData,
    enableColumnActions: false,
    enableColumnFilters: false,
    enableDensityToggle: false,
    enableFullScreenToggle: false,
    enableHiding: false,
    enableRowSelection: false,
    enableSorting: false,
    onPaginationChange: setPagination,
    paginationDisplayMode: "pagination",
    state: { pagination },
    getRowId: (row) => row.id,
    muiTablePaperProps: {
      sx: () => ({
        borderRadius: "12px",
        boxShadow: "none",
        "& .MuiBox-root": {
          boxShadow: "none",
          alignItems: "center",
        },
      }),
    },
    initialState: {
      showGlobalFilter: true,
    },
    muiPaginationProps: {
      shape: "rounded",
      showRowsPerPage: false,
      showFirstButton: false,
      showLastButton: false,
      fontWeight: "600",
      sx: {
        color: "#475467",
        fontSize: "14px",
        fontFamily: "Inter",
        lineHeight: "20px",
        fontWeight: "500",
        "& .MuiPagination-ul li:last-child": {
          fontWeight: "600 !important",
        },
      },
    },
    muiSearchTextFieldProps: {
      placeholder: "Search",
      className: classes.searchField,
      inputProps: {
        sx: {
          fontFamily: "Inter",
          fontSize: "16px",
          fontWeight: "400",
          border: "none",
        },
      },
      ref: searchRef,
      sx: () => ({
        minWidth: "252px",
        ".MuiInputBase-adornedStart": {
          padding: "0px 6px",
          background: "#fff",
        },
        button: {
          padding: "0px",
          width: "auto",
        },
        "& input": {
          padding: "8px 0",
          fontSize: "15px",
        },
        "&$focused": {
          borderColor: "#099250 !important",
          borderWidth: "1px !important",
          boxShadow: "none !important",
        },
      }),
      variant: "outlined",
      InputProps: {
        startAdornment: (
          <LuSearch
            style={{
              color: "#9AA4B2",
              fontSize: "20px",
              marginRight: "10px",
            }}
          />
        ),
      },
    },
    muiTableHeadCellProps: {
      sx: () => ({
        display: "flex",
        flex: "1 1 0",
        fontFamily: "Inter",
        textTransform: "uppercase",
        color: "#9AA4B2",
        fontSize: "10px",
        lineHeight: "19px",
        fontWeight: "700",
        padding: "6px 16px",
        borderBottom: "none",
      }),
    },
    muiTableHeadRowProps: {
      sx: () => ({
        display: "flex",
        flexDirection: "row",
        flex: "1 1 0",
        height: "52px",
        boxShadow: "none !important",
      }),
    },
    muiTopToolbarProps: {
      sx: () => ({
        "& .MuiBox-root": {
          flexDirection: "row-reverse",
          justifyContent: "space-between",
          padding: "3px !important",
        },
        "& input: focus": {
          borderColor: "#099250 !important",
          borderWidth: "1px !important",
          boxShadow: "none !important",
        },
      }),
    },
    muiTableBodyProps: {
      sx: () => ({
        "& tr": {
          display: "flex",
          flex: "1 1 0",
          flexDirection: "row",
          width: "100%",
          height: "52px",
          borderRadius: "8px",
          backgroundColor: "#ffffff",
          margin: "8px 0px",
          overflow: "hidden",
          border: "1px solid #e0e0e0",
          boxShadow: "0px 1px 2px 0px #1018280F",
          boxShadow: "0px 1px 3px 0px #1018281A",
        },
        "& td": {
          flex: "1 1 0",
          display: "flex", // Make the cell a flex container
          alignItems: "center", // Center content vertically
          padding: "6px 16px", // Add padding inside cells
          whiteSpace: "nowrap", // Prevent text from wrapping
          overflow: "hidden", // Hide overflow
          borderBottom: "none", // Remove bottom border between cells
          fontFamily: "Inter",
        },
      }),
    },
    muiTableBodyRowProps: ({ row }) => ({
      onClick: () => {},
    }),
    renderTopToolbarCustomActions: () => <CustomTableToolbar />,
  });

  return (
    <>
      <Loader open={open} />
      <Container>
        <div className="d-flex align-items-center">
          <h4 className="m-0 p-0">Users</h4>
        </div>
        {selectedUser && Object.keys(selectedUser).length > 0 && (
          <UsersModal
            handleClose={handleClose}
            editModalStatus={editModalStatus}
            deleteModalStatus={deleteModalStatus}
            modalInfo={modalInfo}
            userData={selectedUser}
          />
        )}
        <div className="tab-content" id="users-tab">
          <div>
            <MaterialReactTable table={customTable} />
          </div>
          <StyledPagination
            page={pagination.pageIndex + 1}
            count={Math.floor(usersData.length / pagination.pageSize)}
            onChange={handlePageChange}
          />
        </div>
      </Container>
    </>
  );
}

export default UsersTableWrapper;
