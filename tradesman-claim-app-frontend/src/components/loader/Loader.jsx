import React from "react";
import Backdrop from "@mui/material/Backdrop";
import CircularProgress from "@mui/material/CircularProgress";

function Loader({ open, isForModal }) {
  return (
    <>
      <Backdrop
        sx={{
          color: isForModal ? "#00017" : "#fff",
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: isForModal ? 'transparent' : '',
        }}
        open={open}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
    </>
  );
}

export default Loader;
