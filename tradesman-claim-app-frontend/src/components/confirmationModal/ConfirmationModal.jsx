import React from 'react';
import { Button, Modal } from 'react-bootstrap';

function ConfirmationModal({
  showModel,
  onClose,
  title,
  buttonValue,
  onSuccess,
  message
}) {

  return (
    <>
      <Modal
        show={showModel}
        onHide={onClose}
        backdrop="static"
        keyboard={false}
      >
        <Modal.Header className='border-0'>
          <Modal.Title>{title}</Modal.Title>
        </Modal.Header>
        <Modal.Body className='border-0'>{message}</Modal.Body>
        <Modal.Footer className='border-0'>
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="dark" onClick={onSuccess}>{buttonValue}</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default ConfirmationModal
