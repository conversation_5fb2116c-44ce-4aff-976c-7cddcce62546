import React, { useState, useEffect } from "react";
import { Modal } from "react-bootstrap";
import Loader from "../loader/Loader";
import ClaimForm from "./ClaimForm";
import { MdClose } from "react-icons/md";

function ClaimModal({ addModalStatus, onUpdate, handleClose, claimData }) {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Loader open={open} />
      <Modal show={addModalStatus} onHide={handleClose}>
        <div className="p-4">
          <div className="d-flex justify-content-end">
            <div className="close-icon-wrapper" onClick={handleClose}>
              <MdClose className="close-icon" />
            </div>
          </div>
          <div className="tab-content position-relative">
            <ClaimForm
              initialValues={claimData}
              onUpdate={onUpdate}
              handleClose={handleClose}
            />
          </div>
        </div>
      </Modal>
    </>
  );
}

export default ClaimModal;
