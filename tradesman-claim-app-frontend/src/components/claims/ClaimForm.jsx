import React from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Form, Button, Row, Col } from "react-bootstrap";
import FIELD_CONFIG from "./fieldConfig";
import { handleCompanyDetails } from "../../services/companies";
import { handleClaimDetails } from "../../services/claims";
import { useSelector } from "react-redux";

const generateValidationSchema = (userPermissions) => {
  const schema = {};
  Object.entries(FIELD_CONFIG)
    .filter(([key, field]) =>
      userPermissions?.some(
        (p) => p.field_name === field.fieldName && p.is_viewable === "Y",
      ),
    )
    .forEach(([key, field]) => {
      let validator;

      // Determine the base validator based on field type
      if (field.type === "number") {
        validator = Yup.number()
          .typeError(`${field.label} is required`)
          .transform((value, originalValue) => {
            if (originalValue === "" || originalValue === null) {
              return null; // Allow empty strings and null to be null
            }
            const parsed = Number(originalValue);
            return isNaN(parsed) ? undefined : parsed; // Handle invalid numbers
          })
          .nullable(); // Allow null values, but required will override this
      } else if (field.type === "date") {
        validator = Yup.date().nullable();
      } else {
        validator = Yup.string().nullable();
      }

      // Check if the user has permission to edit the field
      const hasEditPermission = userPermissions?.some(
        (p) => p.field_name === field.fieldName && p.is_editable === "Y",
      );

      // Only mark as required if the user can edit the field
      if (field.required && hasEditPermission) {
        validator = validator.required(`${field.label} is required`);
      }

      schema[field.fieldName] = validator; // Use field.fieldName as the key
    });
  return Yup.object().shape(schema);
};

const FormField = ({ field, formik, userPermissions }) => {
  if (!field || !formik) return null;

  // Find permission for this field
  const fieldPermission = userPermissions?.find(
    (p) => p.field_name === field.fieldName,
  );

  // If field isn't in permissions or not viewable, don't render
  if (!fieldPermission || fieldPermission.is_viewable !== "Y") {
    return null;
  }

  // Determine if field should be readonly/disabled
  const isDisabled = fieldPermission.is_editable !== "Y";

  // Inside the FormField component:
  console.log(
    "FormField:",
    field.fieldName,
    "value:",
    formik.values[field.fieldName],
  );

  switch (field.type) {
    case "textarea":
      return (
        <Form.Group className="mb-3">
          <Form.Label>
            {field.label}
            {field.required && "*"}
          </Form.Label>
          <Form.Control
            as="textarea"
            rows={3}
            name={field.fieldName}
            value={formik.values[field.fieldName] || ""}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            isInvalid={
              formik.touched[field.fieldName] && formik.errors[field.fieldName]
            }
            disabled={isDisabled}
            className="form-control"
          />
          <Form.Control.Feedback type="invalid">
            {formik.errors[field.fieldName]}
          </Form.Control.Feedback>
        </Form.Group>
      );

    case "select": // Keep select logic for other fields that still use it.
      return (
        <Form.Group className="mb-3">
          <Form.Label>
            {field.label}
            {field.required && "*"}
          </Form.Label>
          <Form.Select
            name={field.fieldName}
            value={formik.values[field.fieldName] || ""}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            isInvalid={
              formik.touched[field.fieldName] && formik.errors[field.fieldName]
            }
            disabled={isDisabled}
            className="form-select"
          >
            <option value="">Select An Option</option>
            {field.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Form.Select>
          <Form.Control.Feedback type="invalid">
            {formik.errors[field.fieldName]}
          </Form.Control.Feedback>
        </Form.Group>
      );

    default: // Handles text, number, and date inputs
      return (
        <Form.Group className="mb-3">
          <Form.Label>
            {field.label}
            {field.required && "*"}
          </Form.Label>
          <Form.Control
            type={field.type}
            name={field.fieldName}
            value={formik.values[field.fieldName] || ""}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            isInvalid={
              formik.touched[field.fieldName] && formik.errors[field.fieldName]
            }
            disabled={isDisabled}
            step="any"
            className="form-control"
          />
          <Form.Control.Feedback type="invalid">
            {formik.errors[field.fieldName]}
          </Form.Control.Feedback>
        </Form.Group>
      );
  }
};

function ClaimForm({ initialValues = {}, onUpdate, handleClose }) {
  console.log(initialValues);
  const userDetails = useSelector((state) => state.auth.userDetails);
  const { permissions: userPermissions, role: userRole } = userDetails;

  const [companiesData, setCompaniesData] = React.useState([]);
  const [lawFirmName, setLawFirmName] = React.useState("");

  const getCompaniesData = async () => {
    try {
      const response = await handleCompanyDetails.getCompanies({
        limit: 1000,
      });
      setCompaniesData(response.companies_data || []);
      const LawFirm = response.companies_data.find(
        (lawFirm) => lawFirm.id === initialValues.law_firm_id,
      );
      setLawFirmName(LawFirm.title);
      console.log("companiesData", response.companies_data);
    } catch (error) {
      console.error("Error fetching companies:", error);
      setCompaniesData([]);
    }
  };

  React.useEffect(() => {
    getCompaniesData();
  }, []);

  const defaultValues = Object.values(FIELD_CONFIG).reduce((acc, field) => {
    acc[field.fieldName] = "";
    return acc;
  }, {});

  const formik = useFormik({
    initialValues: {
      ...defaultValues,
      ...initialValues,
    },
    validationSchema: generateValidationSchema(userPermissions),
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const submitData = {
          ...values,
          id: initialValues.id,
          status: "Active",
        };
        const response = await handleClaimDetails.updateDetails(submitData);

        if (response.status === "Success") {
          onUpdate();
          handleClose();
        } else {
          console.error("Failed to update claim:", response.message);
        }
      } catch (error) {
        console.error("Error updating claim:", error);
      } finally {
        setSubmitting(false);
      }
    },
    enableReinitialize: true,
  });
  console.log("formik.values:", formik.values);

  if (!FIELD_CONFIG) return null;

  return (
    <Form onSubmit={formik.handleSubmit} className="w-100">
      <h4 className="text-left mb-4">
        Claim # {formik.values["claim_number"]}
      </h4>
      <h5 className="text-left">Assigned to: {lawFirmName}</h5>
      <Row>
        {Object.values(FIELD_CONFIG).map((fieldConfig) => {
          const isAllowed = userPermissions?.some(
            (p) =>
              p.field_name === fieldConfig.fieldName && p.is_viewable === "Y",
          );

          if (!isAllowed) return null;

          return (
            <Col
              md={fieldConfig.col || 12}
              className="mb-3"
              key={fieldConfig.fieldName}
            >
              <FormField
                field={fieldConfig} // Pass the entire fieldConfig
                formik={formik}
                userRole={userRole}
                userPermissions={userPermissions}
                companiesData={companiesData}
              />
            </Col>
          );
        })}
      </Row>

      <div className="mt-4">
        <div className="d-flex gap-3 justify-content-center">
          <div className="d-flex w-50">
            <Button className="btn cancel-btn" onClick={handleClose}>
              Cancel
            </Button>
          </div>
          <div className="d-flex w-50">
            <Button className="btn auth-page-btn" type="submit">
              Save
            </Button>
          </div>
        </div>
      </div>
    </Form>
  );
}

export default ClaimForm;
