import { styled } from "@mui/system";
import { Pagination } from "@mui/material";

// Common styles
export const COMMON_STYLES = {
  fonts: {
    inter: "Inter",
  },
  colors: {
    primary: "#099250",
    text: {
      primary: "#475467",
      secondary: "#9AA4B2",
    },
    border: "#EAECF0",
    hover: "#ECFDF3",
    background: {
      white: "#FFFFFF",
      hover: "#F9FAFB",
    },
  },
  shadows: {
    tableRow: "0px 1px 2px 0px #1018280F, 0px 1px 3px 0px #1018281A",
  },
};

// Styled Components
export const StyledPagination = styled(Pagination)({
  className: "myStyledPagination",
  display: "flex",
  position: "relative",
  marginTop: "-40px",
  zIndex: "999",
  justifyContent: "center",
  width: "100%",
  color: "#475467 !important",
  "& .MuiPagination-ul": {
    width: "100%",
    justifyContent: "center",
    padding: "0 16px",
  },
  "& .MuiPagination-ul li": {
    color: "#475467",
  },
  "& .MuiPagination-ul li:last-child, & .MuiPagination-ul li:first-of-type": {
    fontWeight: "600 !important",
  },
  "& .MuiPagination-ul li:last-child": {
    marginLeft: "auto",
  },
  "& .MuiPagination-ul li:last-child button::before": {
    content: "'Next'",
    marginRight: "8px",
  },
  "& .MuiPagination-ul li:first-of-type": {
    marginRight: "auto",
  },
  "& .MuiPagination-ul li:first-of-type button::after": {
    content: "'Previous'",
    marginLeft: "8px",
  },
  "& .MuiPaginationItem-root": {
    fontFamily: "Inter",
    fontSize: "14px !important",
    lineHeight: "20px",
    fontWeight: "500",
    boxShadow: "none",
  },
  "& .MuiPaginationItem-page": {
    height: "40px",
    width: "40px",
  },
  "& .MuiPaginationItem-page:hover": {
    backgroundColor: "#ECFDF3",
  },
  "& .MuiPaginationItem-page.Mui-selected": {
    backgroundColor: "#FFF",
    color: "#099250",
    border: "1px solid #CDD5DF",
    borderRadius: "8px",
  },
  "& .MuiPaginationItem-ellipsis": {
    paddingTop: "11px",
  },
});

// Table Style Configurations
export const TABLE_STYLES = {
  paper: {
    sx: {
      borderRadius: "12px",
      boxShadow: "none",
      "& .MuiBox-root": {
        boxShadow: "none",
        alignItems: "center",
      },
    },
  },
  searchField: {
    placeholder: "Search",
    variant: "outlined",
    sx: {
      minWidth: "252px",
      ".MuiInputBase-adornedStart": {
        padding: "0px 6px",
        background: COMMON_STYLES.colors.background.white,
      },
      "& input": {
        padding: "8px 0",
        fontSize: "15px",
      },
      "&:focus": {
        borderColor: `${COMMON_STYLES.colors.primary} !important`,
        borderWidth: "1px !important",
        boxShadow: "none !important",
      },
    },
  },
  headCell: {
    sx: {
      display: "flex",
      flex: "1 1 auto", // Changed from "1 1 0"
      fontFamily: COMMON_STYLES.fonts.inter,
      textTransform: "uppercase",
      color: COMMON_STYLES.colors.text.secondary,
      fontSize: "10px",
      lineHeight: "19px",
      fontWeight: "700",
      padding: "6px 16px",
      borderBottom: "none",
    },
  },
  headRow: {
    sx: {
      display: "flex",
      flexDirection: "row",
      flex: "1 1 0",
      height: "52px",
      boxShadow: "none !important",
    },
  },
  topToolbar: {
    sx: {
      "& .MuiBox-root": {
        flexDirection: "row-reverse",
        justifyContent: "space-between",
        padding: "3px !important",
      },
      "& input: focus": {
        borderColor: `${COMMON_STYLES.colors.primary} !important`,
        borderWidth: "1px !important",
        boxShadow: "none !important",
      },
    },
  },
  tableBody: {
    sx: {
      "& tr": {
        display: "flex",
        flex: "1 1 auto", // Changed from "1 1 0"
        flexDirection: "row",
        width: "100%",
        height: "52px",
        borderRadius: "8px",
        backgroundColor: COMMON_STYLES.colors.background.white,
        margin: "8px 0px",
        overflow: "hidden",
        border: `1px solid ${COMMON_STYLES.colors.border}`,
        boxShadow: COMMON_STYLES.shadows.tableRow,
      },
      "& td": {
        flex: "1 1 auto", // Changed from "1 1 0"
        display: "flex",
        alignItems: "center",
        padding: "6px 16px",
        borderBottom: "none",
        fontFamily: COMMON_STYLES.fonts.inter,
      },
    },
  },
  tableBodyRow: {
    sx: {
      cursor: "pointer",
      "&:hover": {
        backgroundColor: COMMON_STYLES.colors.hover,
      },
    },
  },
  pagination: {
    shape: "rounded",
    showRowsPerPage: false,
    showFirstButton: false,
    showLastButton: false,
    sx: {
      color: COMMON_STYLES.colors.text.primary,
      fontSize: "14px",
      fontFamily: COMMON_STYLES.fonts.inter,
      lineHeight: "20px",
      fontWeight: "500",
      "& .MuiPagination-ul li:last-child": {
        fontWeight: "600 !important",
      },
    },
  },
};
