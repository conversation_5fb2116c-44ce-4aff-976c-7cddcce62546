export const FIELD_CONFIG = {
  broker_id: {
    type: "number",
    label: "Broker ID #",
    required: false,
    col: 6,
    fieldName: "broker_id",
  },
  broker_name: {
    type: "text",
    label: "Broker Name",
    required: false,
    col: 6,
    fieldName: "broker_name",
  },
  case_analysis: {
    type: "textarea",
    label: "Case Analysis",
    required: false,
    col: 12,
    fieldName: "case_analysis",
  },
  case_fact: {
    type: "textarea",
    label: "Case Facts",
    required: false,
    col: 12,
    fieldName: "case_fact",
  },
  case_name: {
    type: "text",
    label: "Case Name",
    required: false,
    col: 6,
    fieldName: "case_name",
  },
  case_concern_to_sm: {
    type: "select",
    label: "Case of Concern to Senior Management? (Y/N)",
    required: false,
    col: 6,
    fieldName: "case_concern_to_sm",
    options: [
      { value: "Y", label: "Yes" },
      { value: "N", label: "No" },
    ],
  },
  ctt_or_cts: {
    type: "select",
    label: "Case to Try or Case to Settle",
    required: false,
    col: 6,
    fieldName: "ctt_or_cts",
    options: [
      { value: "CTT", label: "Case to Try" },
      { value: "CTS", label: "Case to Settle" },
    ],
  },
  claim_adjuster: {
    type: "text",
    label: "Claim Adjuster",
    required: false,
    col: 6,
    fieldName: "claim_adjuster",
  },
  claim_number: {
    type: "text",
    label: "Claim Number",
    required: false,
    col: 6,
    fieldName: "claim_number",
  },
  claim_status: {
    type: "select",
    label: "Claim Status (Open/Closed)",
    required: false,
    col: 6,
    fieldName: "claim_status",
    options: [
      { value: "OP", label: "Open" },
      { value: "CL", label: "Closed" },
      { value: "RO", label: "Reopened" },
    ],
  },
  claim_type: {
    type: "select",
    label: "Claim Type",
    required: false,
    col: 6,
    fieldName: "claim_type",
    options: [
      { value: "NY-LL", label: "NY-LL" },
      { value: "BI", label: "BI" },
      { value: "PD", label: "PD" },
    ],
  },
  claimant_name: {
    type: "text",
    label: "Claimant Name",
    required: false,
    col: 6,
    fieldName: "claimant_name",
  },
  date_of_loss: {
    type: "date",
    label: "Date of Loss",
    required: false,
    col: 6,
    fieldName: "date_of_loss",
  },
  date_of_loss_notes: {
    type: "textarea",
    label: "Date of Loss Notes",
    required: false,
    col: 12,
    fieldName: "date_of_loss_notes",
  },
  effective_date: {
    type: "date",
    label: "Effective Date",
    required: false,
    col: 6,
    fieldName: "effective_date",
  },
  expense_incurred: {
    type: "number",
    label: "Expense - Incurred",
    required: false,
    col: 6,
    fieldName: "expense_incurred",
  },
  expense_outstanding: {
    type: "number",
    label: "Expense - Outstanding",
    required: false,
    col: 6,
    fieldName: "expense_outstanding",
  },
  expense_paid: {
    type: "number",
    label: "Expense Paid",
    required: false,
    col: 6,
    fieldName: "expense_paid",
  },
  firm_matter_number: {
    type: "text",
    label: "Firm Matter Number",
    required: false,
    col: 6,
    fieldName: "firm_matter_number",
  },
  indemnity_incurred: {
    type: "number",
    label: "Indemnity - Incurred",
    required: false,
    col: 6,
    fieldName: "indemnity_incurred",
  },
  indemnity_outstanding: {
    type: "number",
    label: "Indemnity - Outstanding",
    required: false,
    col: 6,
    fieldName: "indemnity_outstanding",
  },
  indemnity_paid: {
    type: "number",
    label: "Indemnity Paid",
    required: false,
    col: 6,
    fieldName: "indemnity_paid",
  },
  injury_loss: {
    type: "textarea",
    label: "Injury/Damages",
    required: false,
    col: 12,
    fieldName: "injury_loss",
  },
  insured_defendant_name: {
    type: "text",
    label: "Insured Defendant Name(s)",
    required: false,
    col: 12,
    fieldName: "insured_defendant_name",
  },
  litigation_phase: {
    type: "text",
    label: "Litigation Phase (In Discovery/Deposition, etc)",
    required: false,
    col: 6,
    fieldName: "litigation_phase",
  },
  loss_description: {
    type: "textarea",
    label: "Loss Description",
    required: false,
    col: 12,
    fieldName: "loss_description",
  },
  medical_provider: {
    type: "textarea",
    label: "Medical Provider(s)",
    required: false,
    col: 12,
    fieldName: "medical_provider",
  },
  plaintiff_attorny: {
    type: "text",
    label: "Plaintiff Attorney",
    required: false,
    col: 6,
    fieldName: "plaintiff_attorny",
  },
  plaintiff_law_firm: {
    type: "text",
    label: "Plaintiff Law Firm",
    required: false,
    col: 6,
    fieldName: "plaintiff_law_firm",
  },
  policy_number: {
    type: "text",
    label: "Policy Number",
    required: false,
    col: 12,
    fieldName: "policy_number",
  },
  property_damage_incurred: {
    type: "number",
    label: "Property Damage - Incurred",
    required: false,
    col: 6,
    fieldName: "property_damage_incurred",
  },
  property_damage_outstanding: {
    type: "number",
    label: "Property Damage - Outstanding",
    required: false,
    col: 6,
    fieldName: "property_damage_outstanding",
  },
  property_damage_paid: {
    type: "number",
    label: "Property Damage Paid",
    required: false,
    col: 6,
    fieldName: "property_damage_paid",
  },
  reverse_increase_requested: {
    type: "select",
    label: "Reserve Increase Requested (Y/N?)",
    required: false,
    col: 6,
    fieldName: "reverse_increase_requested",
    options: [
      { value: "Y", label: "Yes" },
      { value: "N", label: "No" },
    ],
  },
  surgeon: {
    type: "text",
    label: "Surgeon(s)",
    required: false,
    col: 6,
    fieldName: "surgeon",
  },
  tradesman_comments: {
    type: "textarea",
    label: "Tradesman Comments",
    required: false,
    col: 12,
    fieldName: "tradesman_comments",
  },
  trail_date: {
    type: "date",
    label: "Trial Date",
    required: false,
    col: 6,
    fieldName: "trail_date",
  },
  trail_information: {
    type: "textarea",
    label: "Trial Information",
    required: false,
    col: 12,
    fieldName: "trail_information",
  },
  venue: {
    type: "text",
    label: "Venue",
    required: false,
    col: 6,
    fieldName: "venue",
  },
  defense_counsel: {
    type: "text",
    label: "Defense Counsel",
    required: false,
    col: 6,
    fieldName: "defense_counsel",
  },
  accident_state: {
    type: "text",
    label: "Accident State",
    required: false,
    col: 6,
    fieldName: "accident_state",
  },
  expiration_date: {
    type: "date",
    label: "Expiration Date",
    required: false,
    col: 6,
    fieldName: "expiration_date",
  },
  work_comp_lien: {
    type: "textarea",
    label: "Work Comp Lien",
    required: false,
    col: 12,
    fieldName: "work_comp_lien",
  },
  total_amount_medicals: {
    type: "number",
    label: "Total Amount Medicals",
    required: false,
    col: 6,
    fieldName: "total_amount_medicals",
  },
  other_demages: {
    type: "textarea",
    label: "Other Damages",
    required: false,
    col: 12,
    fieldName: "other_demages",
  },
  layer_aditional_comments: {
    type: "textarea",
    label: "Lawyer Additional Comments",
    required: false,
    col: 12,
    fieldName: "layer_aditional_comments",
  },
  settlement_value: {
    type: "text",
    label: "Settlement Value",
    required: false,
    col: 6,
    fieldName: "settlement_value",
  },
  verdict_value: {
    type: "text",
    label: "Verdict Value",
    required: false,
    col: 6,
    fieldName: "verdict_value",
  },
  propose_reserve_increase: {
    type: "number",
    label: "Proposed Reserve Increase",
    required: false,
    col: 6,
    fieldName: "propose_reserve_increase",
  },
  first_chair: {
    type: "text",
    label: "1st Chair",
    required: false,
    col: 6,
    fieldName: "1st_chair",
  },
  second_chair: {
    type: "text",
    label: "2nd Chair",
    required: false,
    col: 6,
    fieldName: "2nd_chair",
  },
  potential_case: {
    type: "select",
    label: "Potential 240(1) Case",
    required: false,
    col: 6,
    fieldName: "potential_case",
    options: [
      { value: "Yes", label: "Yes" },
      { value: "No", label: "No" },
    ],
  },
  industrial_code_voilation_alleged: {
    type: "text",
    label: "Industrial Code Violation Alleged",
    required: false,
    col: 12,
    fieldName: "industrial_code_voilation_alleged",
  },
  industrial_code_voilation_opinion: {
    type: "text",
    label: "Is there a Violation of the Industrial Code In Your Opinion",
    required: false,
    col: 12,
    fieldName: "industrial_code_voilation_opinion",
  },
};

export default FIELD_CONFIG;
