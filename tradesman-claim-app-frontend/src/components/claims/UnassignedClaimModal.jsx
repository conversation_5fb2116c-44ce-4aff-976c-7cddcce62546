import React, { useState, useEffect } from "react";
import { <PERSON>dal, Form, Button, Alert } from "react-bootstrap";
import { useFormik } from "formik";
import * as Yup from "yup";
import Loader from "../loader/Loader";
import { MdClose } from "react-icons/md";
import { handleClaimDetails } from "../../services/claims";
import { handleCompanyDetails } from "../../services/companies";

function ClaimModal({ addModalStatus, claimData, handleClose, onUpdate }) {
  const [open, setOpen] = useState(false);
  const [errorResponse, setErrorResponse] = useState([]);
  const [companies, setCompanies] = useState([]);

  // Fetch companies when modal opens
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const response = await handleCompanyDetails.getCompanies({
          limit: 1000,
        });
        setCompanies(response.companies_data);
      } catch (error) {
        console.error("Error fetching companies:", error);
        setCompanies([]);
      }
    };

    if (addModalStatus) {
      fetchCompanies();
    }
  }, [addModalStatus]);

  const validationSchema = Yup.object({
    firm_id: Yup.string().required("Please select a company"),
  });

  const formik = useFormik({
    initialValues: {
      firm_id: claimData.law_firm_id || "",
    },
    validationSchema,
    onSubmit: async (values) => {
      setErrorResponse([]);
      setOpen(true);
      try {
        const response = await handleClaimDetails.assignClaims({
          ids: [claimData.id], // Single claim ID
          firm_id: parseInt(values.firm_id), // Convert to number
        });

        if (response.status === "Success") {
          onUpdate();
          handleClose();
        } else {
          setErrorResponse([response.message]);
        }
      } catch (error) {
        setErrorResponse(["An error occurred while assigning the claim."]);
      } finally {
        setOpen(false);
      }
    },
  });

  return (
    <>
      <Loader open={open} />
      <Modal show={addModalStatus} onHide={handleClose}>
        <div className="p-4">
          <div className="d-flex justify-content-between">
            <h4 className="text-left mb-4">Assign Claim</h4>
            <div className="d-flex justify-content-end">
              <div className="close-icon-wrapper" onClick={handleClose}>
                <MdClose className="close-icon" />
              </div>
            </div>
          </div>

          {errorResponse.length > 0 && (
            <Alert variant="danger" className="mb-3">
              {errorResponse.map((error, index) => (
                <div key={index}>{error}</div>
              ))}
            </Alert>
          )}

          <Form onSubmit={formik.handleSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Select Law Firm</Form.Label>
              <Form.Select
                name="firm_id"
                value={formik.values.firm_id}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                isInvalid={formik.touched.firm_id && formik.errors.firm_id}
              >
                <option value="">Select a Law Firm</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.title}
                  </option>
                ))}
              </Form.Select>
              <Form.Control.Feedback type="invalid">
                {formik.errors.firm_id}
              </Form.Control.Feedback>
            </Form.Group>

            <div className="d-flex gap-3 justify-content-center mt-4">
              <div className="d-flex w-50">
                <Button className="btn cancel-btn" onClick={handleClose}>
                  Cancel
                </Button>
              </div>
              <div className="d-flex w-50">
                <Button
                  className="btn auth-page-btn"
                  type="submit"
                  disabled={formik.isSubmitting}
                >
                  Assign
                </Button>
              </div>
            </div>
          </Form>
        </div>
      </Modal>
    </>
  );
}

export default ClaimModal;
