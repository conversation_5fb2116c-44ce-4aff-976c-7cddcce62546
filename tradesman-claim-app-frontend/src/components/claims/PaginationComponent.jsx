import * as React from 'react';
import Typography from '@mui/material/Typography';
import Pagination from '@mui/material/Pagination';
import Stack from '@mui/material/Stack';

export default function PaginationComponent({ pagination, setPagination, totalCount }) {
  const handleChange = (event, value) => {
    setPagination({ ...pagination, pageIndex: value - 1 });
  };

  return (
    <Stack spacing={2}>
      <Typography>Page: {pagination.pageIndex + 1}</Typography>
      <Pagination
        count={Math.ceil(totalCount / pagination.pageSize)}
        page={pagination.pageIndex + 1}
        onChange={handleChange}
      />
    </Stack>
  );
}
