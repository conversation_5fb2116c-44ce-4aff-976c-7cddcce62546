import React, { useState, useMemo, useRef, useEffect } from "react";
import {
  useMaterialReactTable,
  MaterialReactTable,
} from "material-react-table";
import { Container, Button } from "react-bootstrap";
import ClaimsTableFilters from "./ClaimsTableFilters";
import Loader from "../../components/loader/Loader";
import ClaimModal from "./ClaimModal";
import UnassignedClaimModal from "./UnassignedClaimModal";
import "../common/tableStyles.css";
import { LuSearch, LuPen } from "react-icons/lu";
import Chip from "@mui/material/Chip";
import { COMMON_STYLES, TABLE_STYLES, StyledPagination } from "./tableStyles";
import { handleClaimDetails } from "../../services/claims";
import { useStyles } from "../common/styles";
import TableContainer from "@mui/material/TableContainer";
import { handleCookies } from "../../utils/cookies";
import { handleCompanyDetails } from "../../services/companies";

const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

function ClaimsTableWrapper() {
  const [open, setOpen] = useState(false);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [claimsData, setClaimsData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [addModalStatus, setAddModalStatus] = useState(false);
  const [reassignModalStatus, setReassignModalStatus] = useState(false);
  const searchRef = useRef(null);
  const [selectedFilterValue, setSelectedFilterValue] = useState(0);
  const [globalFilter, setGlobalFilter] = useState("");
  const [selectedClaim, setSelectedClaim] = useState({});
  const [companiesData, setCompaniesData] = useState([]);

  const currentUserData = handleCookies.fetchCookies();
  const classes = useStyles();
  const debouncedSearchValue = useDebounce(globalFilter, 100);

  const getCompaniesData = async () => {
    try {
      const response = await handleCompanyDetails.getCompanies({
        limit: 1000,
      });
      setCompaniesData(response.companies_data || []);
    } catch (error) {
      console.error("Error fetching companies:", error);
      setCompaniesData([]);
    }
  };

  useEffect(() => {
    getClaimsData(selectedFilterValue, debouncedSearchValue);
    getCompaniesData();
  }, [
    debouncedSearchValue,
    pagination.pageIndex,
    pagination.pageSize,
    selectedFilterValue,
  ]);

  useEffect(() => {
    console.log("companiesData", companiesData);
  }, [companiesData]);

  const handleClaimClick = (rowData) => {
    setSelectedClaim(rowData);
    setAddModalStatus(true);
    setReassignModalStatus(false);
  };

  const handleReassignClaimClick = (rowData) => {
    setSelectedClaim(rowData);
    setAddModalStatus(false);
    setReassignModalStatus(true);
  };

  const handleClose = () => {
    setSelectedClaim({});
    setAddModalStatus(false);
    setReassignModalStatus(false);
  };

  const onUpdate = async () => {
    getClaimsData(selectedFilterValue, debouncedSearchValue);
  };

  useEffect(() => {
    if (searchRef.current) {
      clearSearchField();
    }
  }, [searchRef.current]);

  const clearSearchField = () => {
    if (searchRef.current) {
      const inputElement = searchRef.current.querySelector(
        ".MuiInputBase-input",
      );
      if (inputElement) {
        inputElement.value = "";
        searchRef.current.value = "";
      }
    }
  };

  const handleFilterSelect = (value) => {
    setSelectedFilterValue(value);
    setPagination((prev) => ({
      ...prev,
      pageIndex: 0,
    }));
    getClaimsData(value);
  };

  const CustomTableToolbar = () => (
    <div className="d-flex justify-content-end w-100">
      <div className="table-header search-filter-gap">
        <ClaimsTableFilters
          handleFilterSelect={handleFilterSelect}
          selectedFilterValue={selectedFilterValue}
        />
      </div>
    </div>
  );

  const claimColumns = useMemo(() => {
    const columns = [
      {
        accessorKey: "claim_number",
        header: "Claim Number",
        size: 70,
      },
      {
        accessorFn: (row) => row.claim_status,
        header: "Status",
        muiTableBodyCellProps: {
          sx: {
            flex: "0 0 90px !important",
            minWidth: "90px",
            maxWidth: "90px",
            fontFamily: COMMON_STYLES.fonts.inter,
            color: COMMON_STYLES.colors.text.secondary,
            fontSize: "10px",
          },
        },
        muiTableHeadCellProps: {
          sx: {
            flex: "0 0 90px !important",
            minWidth: "90px",
            maxWidth: "90px",
            fontFamily: "Inter",
            fontWeight: 700,
            fontSize: "10px",
            lineHeight: "19px",
            letterSpacing: "0.01071em",
            display: "flex",
            verticalAlign: "inherit",
            borderBottom: "none",
            textAlign: "left",
            color: "#9AA4B2",
            backgroundColor: "inherit",
            backgroundImage: "inherit",
            textTransform: "uppercase",
          },
        },
        Cell: ({ cell }) => {
          const value = cell.getValue();
          const getStatusDetails = (status) => {
            switch (status) {
              case "OP":
                return { label: "Open", color: "#175CD3", bgColor: "#EFF8FF" };
              case "CL":
                return {
                  label: "Closed",
                  color: "#027A48",
                  bgColor: "#ECFDF3",
                };
              default:
                return { label: status, color: "#B54708", bgColor: "#FFFAEB" };
            }
          };

          const { label, color, bgColor } = getStatusDetails(value);
          return (
            <Chip
              label={label}
              size="small"
              sx={{
                backgroundColor: bgColor,
                color: color,
                margin: "0px",
                padding: "0px",
                height: "auto",
                "& .MuiChip-label": {
                  padding: "2px 8px",
                },
              }}
            />
          );
        },
      },
      {
        accessorFn: (row) => row.insured_defendant_name || "-",
        header: "Insured/Defendant Name",
        size: 125,
      },
      {
        accessorFn: (row) => row.policy_number || "-",
        header: "Policy #",
        size: 75,
      },
      {
        accessorFn: (row) => row.plaintiff_attorny || "-",
        header: "Plaintiff Attorney",
        size: 50,
      },
      {
        accessorFn: (row) => row.plaintiff_law_firm || "-",
        header: "Plaintiff Law Firm",
        size: 50,
      },
      {
        accessorFn: (row) => row.claimant_name || "-",
        header: "Claimant Name",
        size: 125,
      },
    ];
    if (currentUserData?.user_role !== "Lawyer") {
      columns.push({
        accessorFn: (row) => row.law_firm_id || "-",
        id: "law_firm_id",
        header: "Assigned Law Firm",
        size: 125,
        Cell: ({ cell }) => {
          const law_firm_id = cell.getValue();

          if (!law_firm_id || law_firm_id === "-") {
            return "-";
          }
          const LawFirm = companiesData.find(
            (lawFirm) => lawFirm.id === law_firm_id,
          );
          return LawFirm?.title || "-";
        },
      });
      columns.push({
        accessorFn: (row) => row.id,
        header: "",
        id: "id",
        size: 1,
        align: "right",
        muiTableBodyCellProps: {
          sx: {
            flex: "0 0 50px !important",
            minWidth: "50px",
            maxWidth: "50px",
            padding: "0px !important",
            margin: "0px !important",
            gap: "0px !important",
          },
        },
        muiTableHeadCellProps: {
          sx: {
            flex: "0 0 50px !important",
            minWidth: "50px",
            maxWidth: "50px",
            padding: "0px !important",
            margin: "0px !important",
            gap: "0px !important",
            borderBottom: "none",
          },
        },
        Cell: ({ cell }) => (
          <Button
            variant=""
            size="sm"
            onClick={(event) => {
              event.stopPropagation();
              handleReassignClaimClick(cell.row.original);
            }}
          >
            <LuPen />
          </Button>
        ),
      });
    }
    return columns;
  }, [companiesData]);

  const getClaimsData = async (
    filterValue = selectedFilterValue,
    searchValue = debouncedSearchValue,
  ) => {
    setOpen(true);
    try {
      const response = await handleClaimDetails.getClaims({
        offset: pagination.pageIndex * pagination.pageSize,
        limit: pagination.pageSize,
        claim_status: filterValue !== "0" ? filterValue : null,
        search: searchValue || null,
      });

      setClaimsData(response.data.claim_form_data);
      setTotalCount(response.data.pagination.total);
      setOpen(false);
    } catch (error) {
      console.error("Error fetching claims:", error);
      setClaimsData([]);
      setTotalCount(0);
      setOpen(false);
    }
  };

  const handlePageChange = (event, newPage) => {
    setPagination((prev) => ({
      ...prev,
      pageIndex: newPage - 1,
    }));
  };

  const customTable = useMaterialReactTable({
    columns: claimColumns,
    enableRowsPerPage: false,
    muiTableContainerProps: { sx: { tableLayout: "fixed" } },
    displayRowsPerPage: false,
    data: claimsData,
    rowCount: totalCount,
    enablePagination: true,
    manualPagination: true,
    enableColumnActions: false,
    enableColumnFilters: false,
    enableDensityToggle: false,
    enableFullScreenToggle: false,
    enableHiding: false,
    enableRowSelection: false,
    enableSorting: false,
    onPaginationChange: setPagination,
    paginationDisplayMode: "pagination",
    state: {
      pagination,
      globalFilter,
    },
    enableGlobalFilter: true,
    manualFiltering: true,
    onGlobalFilterChange: setGlobalFilter,
    getRowId: (row) => row.id,
    muiTablePaperProps: TABLE_STYLES.paper,
    initialState: { showGlobalFilter: true },
    muiSearchTextFieldProps: {
      ...TABLE_STYLES.searchField,
      className: classes.searchField,
      InputProps: {
        startAdornment: (
          <LuSearch
            style={{
              color: COMMON_STYLES.colors.text.secondary,
              fontSize: "20px",
              marginRight: "10px",
            }}
          />
        ),
      },
    },
    muiTableHeadCellProps: TABLE_STYLES.headCell,
    muiTableHeadRowProps: TABLE_STYLES.headRow,
    muiTopToolbarProps: TABLE_STYLES.topToolbar,
    muiTableBodyProps: TABLE_STYLES.tableBody,
    muiTableBodyRowProps: ({ row }) => ({
      onClick: () => handleClaimClick(row.original),
      sx: TABLE_STYLES.tableBodyRow.sx,
    }),
    muiPaginationProps: {
      showRowsPerPage: false, // This disables rows per page
      showFirstButton: false,
      showLastButton: false,
      rowsPerPageOptions: [], // This removes the options
    },
    renderTopToolbarCustomActions: () => <CustomTableToolbar />,
  });

  return (
    <>
      <Loader open={open} />
      <Container>
        <div className="d-flex align-items-center">
          <h4 className="m-0 p-0">Insurance Claim</h4>
        </div>

        {selectedClaim &&
          addModalStatus &&
          Object.keys(selectedClaim).length > 0 && (
            <ClaimModal
              handleClose={handleClose}
              onUpdate={onUpdate}
              addModalStatus={addModalStatus}
              claimData={selectedClaim}
            />
          )}
        {selectedClaim &&
          reassignModalStatus &&
          Object.keys(selectedClaim).length > 0 && (
            <UnassignedClaimModal
              handleClose={handleClose}
              onUpdate={onUpdate}
              addModalStatus={reassignModalStatus}
              claimData={selectedClaim}
            />
          )}
        <div className="tab-content">
          <div>
            <MaterialReactTable
              table={customTable}
              components={{
                Container: (props) => (
                  <TableContainer sx={{ tableLayout: "fixed" }} {...props} />
                ),
              }}
            />
          </div>
          <StyledPagination
            page={pagination.pageIndex + 1}
            count={Math.ceil(totalCount / pagination.pageSize)}
            onChange={handlePageChange}
          />
        </div>
      </Container>
    </>
  );
}

export default ClaimsTableWrapper;
