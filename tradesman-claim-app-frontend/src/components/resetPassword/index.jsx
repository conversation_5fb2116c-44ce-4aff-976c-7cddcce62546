import Sidebar from '../../pages/sidebar/Sidebar';
import { Contain<PERSON>, Button, Row, Col, Form } from 'react-bootstrap';
import React, { useState } from "react";
import { serviceCall } from '../../services/emailVerify';
import { resetPasswordSchema } from '../../utils/resetValidation'
import { Link } from 'react-router-dom';
import { useFormik } from "formik";
import PasswordField from '../authPages/signup/PasswordField';
import success from '../../assets/images/success.png'
import { useLocation } from 'react-router'

function Index() {
  const location = useLocation();
  const [showPassword, setShowPassword] = useState(false);
  const [sucussfullyReset, setSucussfullyReset] = useState(false);

  const initialValues = {
    password: ''
  };

  const { values, handleChange, handleSubmit, errors, touched } =
    useFormik({
      initialValues,
      validateOnChange: true,
      validateOnBlur: false,
      validationSchema: resetPasswordSchema,

      onSubmit: async (values, action) => {
        let payload = {
          email: location.state.email,
          password: values.password
        }
        const response = await serviceCall.resetPassword(payload);
        if (response.status === 'Success') {
          setShowPassword(false);
          action.resetForm();
          handleResetPassword();
        }
      },
    });
  const handleResetPassword = () => {
    setSucussfullyReset(true);
  }

  return (
    <Container fluid>
      <Row>
        <Col sm={12} md={4} lg={3} className='sidebar-wrapper'>
          <Sidebar />
        </Col>
        {sucussfullyReset ?
          <Col sm={12} md={8} lg={9}>
            <div className='d-flex text-center align-items-center justify-content-center h-100'>
              <div>
                <div className='mx-auto mb-4 success-img'>
                  <img src={success} alt='Sucess' />
                </div>
                <h1>Reset Password</h1>
                <p className='mb-3'>Your password has been successfully reset, <br></br>
                  Click below to Sign in</p>
                <Link to="/login" className='have-account-link'>
                  <Button className='mt-3 signup-btn' type="submit">
                    Back to Sign In
                  </Button>
                </Link>
              </div>
            </div>
          </Col>
          :
          <Col sm={12} md={8} lg={9}>
            <div className='d-flex h-100 align-items-center justify-content-center'>
              <div>
                <h1>Reset Password</h1>
                <p className='mb-3'>Please enter your new password</p>
                <Form onSubmit={handleSubmit} className="d-flex flex-column"
                  style={{ maxWidth: '416px' }}>
                  <PasswordField values={values} handleChange={handleChange}
                    errors={errors} touched={touched} showPassword={showPassword}
                    setShowPassword={setShowPassword} placeholder='New Password' />
                  <Button className='mt-3 signup-btn' type="submit">
                    Reset
                  </Button>
                </Form>
              </div>
            </div>
          </Col>
        }
      </Row>
    </Container >
  )
}

export default Index
