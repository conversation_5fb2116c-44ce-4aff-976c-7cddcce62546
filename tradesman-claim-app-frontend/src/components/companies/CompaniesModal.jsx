import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, Button, Alert, Row, Col } from "react-bootstrap";
import { useFormik } from "formik";
import { v4 as uuidv4 } from "uuid";
import companyValidationSchema from "../../utils/companiesValidation";
import Loader from "../loader/Loader";

import {
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Chip,
  TextField,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { styled } from "@mui/system";
import { MdClose } from "react-icons/md";

import { handleCompanyDetails } from "../../services/companies";

const TradesmanSelect = styled(Select)`
  & .MuiSelect-root {
    padding: 10px 16px;
    border-radius: 8px;
    border: 1px solid #eaecf0;
    width: 100%;
    margin: 0px 8px;
    height: 46px;
    &:hover {
      border-color: "red !important";
    }
  }
  & .MuiOutlinedInput-root {
    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      border-width: 1px;
      border-color: #099250;
      box-shadow: 0px 0px 0px 4px #d3f8df;
    }
    & .MuiSelect-icon {
      margin-right: 6px;
      margin-top: -2px;
      width: 20px;
      height: 20px;
    }
    font-family: "Inter";
    font-size: 14px;
    line-height: 22px;
    font-weight: 500;
  }
`;

const useStyles = makeStyles({
  chip: {
    marginTop: "4px !important",
    padding: "2px, 8px, 2px, 8px",
    fontFamily: "Inter !important",
    fontSize: "14px",
    lineHeight: "22px",
    fontWeight: "500 !important",
    textAlign: "center",
  },

  selectRoot: {
    "& .MuiOutlinedInput-root": {
      "&:hover .MuiOutlinedInput-notchedOutline": {
        borderWidth: "1px",
        borderColor: "#099250",
        boxShadow: "0px 0px 0px 4px #D3F8DF",
      },
      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
        borderWidth: "1px",
        borderColor: "#099250",
        boxShadow: "0px 0px 0px 4px #D3F8DF",
      },
      "& .MuiSelect-icon": {
        marginRight: "6px",
        marginTop: "-2px",
        width: "20px",
        height: "20px",
      },
      fontFamily: "Inter",
      fontSize: "14px",
      lineHeight: "22px",
      fontWeight: "500",
    },
  },
});
function CompanyModal({
  addModalStatus,
  companyData,
  deleteModalStatus,
  editModalStatus,
  handleClose,
}) {
  const [open, setOpen] = useState(false);
  const [errorResponse, setErrorResponse] = useState([]);
  const [createUUID, setCreateUUID] = useState("");

  // Reset the form when the modal status changes
  useEffect(() => {
    resetForm();
    setCreateUUID(uuidv4().replace(/-/g, ""));
    setErrorResponse("");
  }, [addModalStatus, editModalStatus, deleteModalStatus]);

  // Initialize Formik Values
  const initialValues = {
    title: companyData.title ?? "",
    id: companyData.id ?? "",
  };

  const {
    values,
    handleChange,
    handleBlur,
    handleSubmit,
    errors,
    touched,
    resetForm,
  } = useFormik({
    initialValues,
    validationSchema: companyValidationSchema,
    validateOnChange: true,
    validateOnBlur: false,
    enableReinitialize: true,
    onSubmit: async (values) => {
      setErrorResponse("");
      setOpen(true);
      try {
        const payload = {
          ...values,
        };
        const response = await handleCompanyDetails.updateDetails(payload);
        if (response.status === "Success") {
        } else {
          setErrorResponse(response.messages);
        }
      } catch (error) {
        setErrorResponse(["An error occurred while submitting the claim."]);
      }
      setOpen(false);
      resetForm();
      handleClose();
    },
  });

  const handleClickAdd = async () => {
    try {
      const response = await handleCompanyDetails.createCompany(values);
      if (response.status == "Success") {
      } else {
        setErrorResponse(response.messages);
      }
    } catch (error) {
      setErrorResponse(["An error occurred while submitting the claim."]);
    }
  };

  const handleClickDelete = async () => {
    try {
      const response = await handleCompanyDetails.deleteCompany({
        id: values.id,
      });
      if (response.status == "Success" || response.status == 200) {
        setOpen(false);
        resetForm();
        handleClose();
      } else {
        setOpen(false);
        setErrorResponse(response.messages);
      }
    } catch (error) {
      setErrorResponse(["An error occurred while submitting the claim."]);
    }
  };

  return (
    <>
      <Loader open={open} />
      <Modal
        show={deleteModalStatus}
        onHide={handleClose}
        style={{ marginTop: "15%" }}
      >
        <div className="p-4">
          <h4 className="text-center">Do you want to delete this law firm?</h4>
          <div className="mt-0">
            <div className="d-flex gap-3 justify-content-center">
              <div className="d-flex w-50">
                <Button className="btn cancel-btn" onClick={handleClose}>
                  Cancel
                </Button>
              </div>
              <div className="d-flex w-50">
                <Button
                  className="btn auth-page-btn"
                  id="auth-page-btn-delete"
                  onClick={handleClickDelete}
                >
                  Delete
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Modal>
      <Modal
        show={addModalStatus}
        onHide={handleClose}
        style={{ marginTop: "15%" }}
      >
        <div className="p-4">
          <div className="d-flex justify-content-between">
            <h4 className="text-left mb-4">Add Law Firm</h4>
            <div className="d-flex justify-content-end">
              <div className="close-icon-wrapper" onClick={handleClose}>
                <MdClose className="close-icon" />
              </div>
            </div>
          </div>
          {errorResponse !== "" && (
            <Alert
              variant="danger"
              className="mb-0"
              style={{ fontSize: "14px" }}
            >
              {errorResponse &&
                errorResponse.map((res, index) => <div key={index}>{res}</div>)}
            </Alert>
          )}
          <div className="tab-content position-relative">
            <Form
              onSubmit={handleClickAdd}
              className="d-block"
              autoComplete="off"
            >
              <div className="pb-2">
                <Row>
                  <Col className="mb-2">
                    <Form.Label className="ui-button-s">
                      Law Firm Name
                    </Form.Label>
                    <Form.Control
                      type="text"
                      placeholder="Name"
                      name="title"
                      value={values.title}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      className={`pwd-field-bg ${errors.title && touched.title ? "auth-page-field-with-error" : null}`}
                    />
                    {errors.title && touched.title && (
                      <div className="auth-page-field-error">
                        {errors.title}
                      </div>
                    )}
                  </Col>
                </Row>
              </div>
              <div className="mt-0">
                <div className="d-flex gap-3 justify-content-center">
                  <div className="d-flex w-50">
                    <Button className="btn cancel-btn" onClick={handleClose}>
                      Cancel
                    </Button>
                  </div>
                  <div className="d-flex w-50">
                    <Button className="btn auth-page-btn" type="submit">
                      Save
                    </Button>
                  </div>
                </div>
              </div>
            </Form>
          </div>
        </div>
      </Modal>
      <Modal
        show={editModalStatus}
        onHide={handleClose}
        style={{ marginTop: "15%" }}
      >
        <div className="p-4">
          <div className="d-flex justify-content-between">
            <h4 className="text-left mb-4">Edit Law Firm</h4>
            <div className="d-flex justify-content-end">
              <div className="close-icon-wrapper" onClick={handleClose}>
                <MdClose className="close-icon" />
              </div>
            </div>
          </div>
          {errorResponse !== "" && (
            <Alert
              variant="danger"
              className="mb-0"
              style={{ fontSize: "14px" }}
            >
              {errorResponse &&
                errorResponse.map((res, index) => <div key={index}>{res}</div>)}
            </Alert>
          )}
          <div className="tab-content position-relative">
            <Form
              onSubmit={handleSubmit}
              className="d-block"
              autoComplete="off"
            >
              <div className="pb-2">
                <Row>
                  <Col className="mb-2">
                    <Form.Label className="ui-button-s">
                      Law Firm Name
                    </Form.Label>
                    <Form.Control
                      type="text"
                      placeholder="Name"
                      name="title"
                      value={values.title}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      className={`pwd-field-bg ${errors.title && touched.title ? "auth-page-field-with-error" : null}`}
                    />
                    {errors.title && touched.title && (
                      <div className="auth-page-field-error">
                        {errors.title}
                      </div>
                    )}
                  </Col>
                </Row>
              </div>
              <div className="mt-0">
                <div className="d-flex gap-3 justify-content-center">
                  <div className="d-flex w-50">
                    <Button className="btn cancel-btn" onClick={handleClose}>
                      Cancel
                    </Button>
                  </div>
                  <div className="d-flex w-50">
                    <Button className="btn auth-page-btn" type="submit">
                      Save
                    </Button>
                  </div>
                </div>
              </div>
            </Form>
          </div>
        </div>
      </Modal>
    </>
  );
}

export default CompanyModal;
