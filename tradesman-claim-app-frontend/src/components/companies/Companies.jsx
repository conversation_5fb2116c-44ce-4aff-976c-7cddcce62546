import React, { useState, useMemo, useRef, useEffect } from "react";
import {
  useMaterialReactTable,
  MaterialReactTable,
} from "material-react-table";
import { Container, But<PERSON> } from "react-bootstrap";
import Loader from "../../components/loader/Loader";
import CompaniesModal from "./CompaniesModal";
import "../common/tableStyles.css";
import { LuSearch, LuTrash2, LuPen } from "react-icons/lu";
import { Pagination } from "@mui/material";
import { styled } from "@mui/system";

import { handleCompanyDetails } from "../../services/companies";

import { useStyles } from "../common/styles";
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

function CompaniesTableWrapper() {
  const [open, setOpen] = useState(false);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [companiesData, setCompaniesData] = useState([]);
  const [addModalStatus, setAddModalStatus] = useState(false);
  const [editModalStatus, setEditModalStatus] = useState(false);
  const [deleteModalStatus, setDeleteModalStatus] = useState(false);
  const [modalInfo, setModalInfo] = useState(false);
  const [globalFilter, setGlobalFilter] = useState("");
  const [totalCount, setTotalCount] = useState(0);
  const searchRef = useRef(null);

  const [selectedCompany, setSelectedCompany] = useState(null);
  const debouncedSearchValue = useDebounce(globalFilter, 100);

  const StyledPagination = styled(Pagination)({
    className: "myStyledPagination",
    display: "flex",
    position: "relative",
    marginTop: "-40px",
    zIndex: "999",
    justifyContent: "center",
    width: "100%",
    color: "#475467 !important",
    "& .MuiPagination-ul": {
      width: "100%",
      justifyContent: "center",
      padding: "0 16px",
      backgroundColor: "#red",
    },
    "& .MuiPagination-ul li": {
      color: "#475467",
    },
    "& .MuiPagination-ul li:last-child, & .MuiPagination-ul li:first-child": {
      fontWeight: "600 !important",
    },
    "& .MuiPagination-ul li:last-child": {
      marginLeft: "auto",
    },
    "& .MuiPagination-ul li:last-child button::before": {
      content: "'Next'",
      marginRight: "8px",
    },
    "& .MuiPagination-ul li:first-child": {
      marginRight: "auto",
    },
    "& .MuiPagination-ul li:first-child button::after": {
      content: "'Previous'",
      marginLeft: "8px",
    },
    "& .MuiPaginationItem-root": {
      fontFamily: "Inter",
      fontSize: "14px !important",
      lineHeight: "20px",
      fontWeight: "500",
      boxShadow: "none",
    },
    "& .MuiPaginationItem-page": {
      height: "40px",
      width: "40px",
    },
    "& .MuiPaginationItem-page:hover": {
      backgroundColor: "#ECFDF3",
    },
    "& .MuiPaginationItem-page.Mui-selected": {
      backgroundColor: "#FFF",
      color: "#099250",
      border: "1px solid #CDD5DF",
      borderRadius: "8px",
    },
    "& .MuiPaginationItem-ellipsis": {
      paddingTop: "11px",
    },
  });

  const classes = useStyles();

  useEffect(() => {
    getCompaniesData(debouncedSearchValue);
  }, [debouncedSearchValue, pagination.pageIndex, pagination.pageSize]);

  const handleCompanyClickAdd = () => {
    setSelectedCompany({});
    setAddModalStatus(true);
    setModalInfo(true);
  };

  const handleCompanyClickEdit = (rowData) => {
    setSelectedCompany(rowData);
    setEditModalStatus(true);
    setModalInfo(true);
  };

  const handleCompanyClickDelete = (rowData) => {
    setSelectedCompany(rowData);
    setEditModalStatus(false);
    setDeleteModalStatus(true);
    setModalInfo(true);
  };

  const handleClose = () => {
    setSelectedCompany(null);
    setAddModalStatus(false);
    setEditModalStatus(false);
    setDeleteModalStatus(false);
    handleReset();
  };

  useEffect(() => {
    clearSearchField();
  }, [searchRef]);

  useEffect(() => {
    if (searchRef.current) {
      clearSearchField();
    }
  }, [searchRef.current]);
  // Reset All Filters
  const clearSearchField = () => {
    const inputElement = searchRef.current.querySelector(".MuiInputBase-input");
    if (inputElement) {
      inputElement.value = "";
      searchRef.current.value = "";
    }
  };

  const handleReset = () => {
    clearSearchField();
    getCompaniesData();
  };

  const CustomTableToolbar = () => {
    return (
      <div className="d-flex justify-content-end w-100">
        <div className="table-header search-filter-gap">
          <Button className="btn auth-page-btn" onClick={handleCompanyClickAdd}>
            Add new Law Firm
          </Button>
        </div>
      </div>
    );
  };

  // Table Headers
  const companyColumns = useMemo(
    () => [
      {
        accessorKey: "title",
        header: "Law Firm",
        size: "auto",
      },
      {
        accessorFn: (row) => row.id,
        id: "id",
        size: 5,
        align: "right",
        Cell: ({ cell }) => {
          return (
            <>
              <Button
                variant=""
                size="sm"
                onClick={() => handleCompanyClickDelete(cell.row.original)}
              >
                <LuTrash2 />
              </Button>
              <Button
                variant=""
                size="sm"
                onClick={() => handleCompanyClickEdit(cell.row.original)}
              >
                <LuPen />
              </Button>
            </>
          );
        },
      },
    ],
    [],
  );

  const getCompaniesData = async (searchValue = debouncedSearchValue) => {
    setOpen(true);
    try {
      const response = await handleCompanyDetails.getCompanies({
        offset: pagination.pageIndex * pagination.pageSize,
        limit: pagination.pageSize,
        search: searchValue || null,
      });
      // Filter out the "unassigned" company that is used for admin
      const filteredCompanies = response.companies_data.filter(
        (company) => company.id !== 0,
      );

      setCompaniesData(filteredCompanies);
      setTotalCount(response.pagination.total);
      setOpen(false);
    } catch (error) {
      console.error("Error fetching users:", error);
      setCompaniesData([]);
      setTotalCount(0);
      setOpen(false);
      throw error;
    }
  };

  const handlePageChange = (event, newPage) => {
    setPagination((prev) => ({
      ...prev,
      pageIndex: newPage - 1,
    }));
  };

  const customTable = useMaterialReactTable({
    columns: companyColumns,
    data: companiesData,
    enableColumnActions: false,
    rowCount: totalCount,
    enablePagination: true,
    manualPagination: true,
    enableColumnFilters: false,
    enableDensityToggle: false,
    enableFullScreenToggle: false,
    enableHiding: false,
    enableRowSelection: false,
    enableSorting: false,
    onPaginationChange: setPagination,
    paginationDisplayMode: "pagination",
    state: {
      pagination,
      globalFilter,
    },
    onGlobalFilterChange: setGlobalFilter,
    getRowId: (row) => row.id,
    muiTablePaperProps: {
      sx: () => ({
        borderRadius: "12px",
        boxShadow: "none",
        "& .MuiBox-root": {
          boxShadow: "none",
          alignItems: "center",
        },
      }),
    },
    initialState: {
      showGlobalFilter: true,
    },
    muiPaginationProps: {
      shape: "rounded",
      showRowsPerPage: false,
      showFirstButton: false,
      showLastButton: false,
      fontWeight: "600",
      sx: {
        color: "#475467",
        fontSize: "14px",
        fontFamily: "Inter",
        lineHeight: "20px",
        fontWeight: "500",
        "& .MuiPagination-ul li:last-child": {
          fontWeight: "600 !important",
        },
      },
    },
    muiSearchTextFieldProps: {
      placeholder: "Search",
      className: classes.searchField,
      inputProps: {
        sx: {
          fontFamily: "Inter",
          fontSize: "16px",
          fontWeight: "400",
          border: "none",
        },
      },
      ref: searchRef,
      sx: () => ({
        minWidth: "252px",
        ".MuiInputBase-adornedStart": {
          padding: "0px 6px",
          background: "#fff",
        },
        button: {
          padding: "0px",
          width: "auto",
        },
        "& input": {
          padding: "8px 0",
          fontSize: "15px",
        },
        "&$focused": {
          borderColor: "#099250 !important",
          borderWidth: "1px !important",
          boxShadow: "none !important",
        },
      }),
      variant: "outlined",
      InputProps: {
        startAdornment: (
          <LuSearch
            style={{
              color: "#9AA4B2",
              fontSize: "20px",
              marginRight: "10px",
            }}
          />
        ),
      },
    },
    muiTableHeadCellProps: {
      sx: () => ({
        display: "flex",
        flex: "1 1 0",
        fontFamily: "Inter",
        textTransform: "uppercase",
        color: "#9AA4B2",
        fontSize: "10px",
        lineHeight: "19px",
        fontWeight: "700",
        padding: "6px 16px",
        borderBottom: "none",
      }),
    },
    muiTableHeadRowProps: {
      sx: () => ({
        display: "flex",
        flexDirection: "row",
        flex: "1 1 0",
        height: "52px",
        boxShadow: "none !important",
      }),
    },
    muiTopToolbarProps: {
      sx: () => ({
        "& .MuiBox-root": {
          flexDirection: "row-reverse",
          justifyContent: "space-between",
          padding: "3px !important",
        },
        "& input: focus": {
          borderColor: "#099250 !important",
          borderWidth: "1px !important",
          boxShadow: "none !important",
        },
      }),
    },
    muiTableBodyProps: {
      sx: () => ({
        "& tr": {
          display: "flex",
          flex: "1 1 0",
          flexDirection: "row",
          width: "100%",
          height: "52px",
          borderRadius: "8px",
          backgroundColor: "#ffffff",
          margin: "8px 0px",
          overflow: "hidden",
          border: "1px solid #e0e0e0",
          boxShadow: "0px 1px 2px 0px #1018280F",
          boxShadow: "0px 1px 3px 0px #1018281A",
        },
        "& td": {
          flex: "1 1 0",
          display: "flex", // Make the cell a flex container
          alignItems: "center", // Center content vertically
          padding: "6px 16px", // Add padding inside cells
          whiteSpace: "nowrap", // Prevent text from wrapping
          overflow: "hidden", // Hide overflow
          borderBottom: "none", // Remove bottom border between cells
          fontFamily: "Inter",
        },
      }),
    },
    muiTableBodyRowProps: ({ row }) => ({
      onClick: () => {},
    }),
    renderTopToolbarCustomActions: () => <CustomTableToolbar />,
  });

  return (
    <>
      <Loader open={open} />
      <Container>
        <div className="d-flex align-items-center">
          <h4 className="m-0 p-0">Law Firms</h4>
        </div>
        {selectedCompany && selectedCompany != null > 0 && (
          <CompaniesModal
            handleClose={handleClose}
            addModalStatus={addModalStatus}
            editModalStatus={editModalStatus}
            deleteModalStatus={deleteModalStatus}
            modalInfo={modalInfo}
            companyData={selectedCompany}
          />
        )}
        <div className="tab-content" id="companies-tab">
          <div>
            <MaterialReactTable table={customTable} />
          </div>
          <StyledPagination
            page={pagination.pageIndex + 1}
            count={Math.ceil(totalCount / pagination.pageSize)}
            onChange={handlePageChange}
          />
        </div>
      </Container>
    </>
  );
}

export default CompaniesTableWrapper;
