import React, { useState } from "react";
import "../style.css";
import Loader from "../../loader/Loader";
import SignInForm from "./SignInForm";
import { Link } from "react-router-dom";
import AuthComponentWrapper from "../authComponentWrapper/AuthComponentWrapper";

function SignIn() {
    const [open, setOpen] = useState(false);
    return (
        <AuthComponentWrapper>
            <Loader open={open} />
            <SignInForm setOpen={setOpen} />
            <div className="text-center mt-4">
                <p className="ui-button-l">
                    New Here? <Link to="/signup">Create an Account</Link>
                </p>
            </div>
        </AuthComponentWrapper>
    );
}

export default SignIn;
