import React, { useState } from "react";
import { Form, Button, Row, Col } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
import { useDispatch } from "react-redux";
import { useFormik } from "formik";
import { signInSchema } from "../../../utils/signinValidation";
import { handleCookies } from "../../../utils/cookies";
import { serviceCall } from "../../../services/signIn";
import { useNavigate } from "react-router-dom";
import { setAuthData } from "../../../redux/features/authSlice";

function SignInForm({ setOpen }) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [errorMessage, setErrorMessage] = useState("");

  const initialValues = {
    email: "",
    password: "",
  };

  const { values, handleBlur, handleChange, handleSubmit, errors, touched } =
    useFormik({
      initialValues,
      validationSchema: signInSchema,
      validateOnChange: true,
      validateOnBlur: false,
      onSubmit: async (values, action) => {
        let payload = {
          email: values.email,
          password: values.password,
        };
        setOpen(true);
        const response = await serviceCall.signIn(payload);
        if (response) {
          setOpen(false);
          if (response.status === "Success") {
            const userData = response.data[0];
            dispatch(setAuthData(userData));
            await handleCookies.setCookies(response.data[0]);
            navigate("/claims", { state: { navigationFlag: true } });
          } else {
            setErrorMessage(response.message || "Invalid email or password");
            action.resetForm();
          }
        }
      },
    });

  return (
    <Form onSubmit={handleSubmit} className="w-100">
      <h3 className="auth-page-title">Log in to your account</h3>
      <div className="auth-page-subtitle">
        Welcome back! Please enter your details.
      </div>

      <Form.Group>
        <Row>
          <Col className="mb-2">
            <Form.Label className="ui-button-s">Email</Form.Label>
            <Form.Control
              type="email"
              name="email"
              id="email"
              placeholder="Enter your email"
              value={values.email}
              onChange={handleChange}
              onBlur={handleBlur}
              className={
                errors.email && touched.email
                  ? "auth-page-field-with-error"
                  : null
              }
            />
            {errors.email && touched.email && (
              <div className="auth-page-field-error">{errors.email}</div>
            )}
          </Col>
        </Row>
        <Row>
          <Col className="mb-2">
            <div className="d-flex justify-content-between">
              <Form.Label className="ui-button-s">Password</Form.Label>
              <Link to="/forgotpassword" className="have-account-link">
                <p className="forgot-pwd-btn">Forgot password?</p>
              </Link>
            </div>
            <Form.Control
              type="password"
              autoComplete="off"
              name="password"
              id="password"
              placeholder="Enter password"
              value={values.password}
              onChange={handleChange}
              onBlur={handleBlur}
              className={
                errors.password && touched.password
                  ? "auth-page-field-with-error"
                  : null
              }
            />
            {errors.password && touched.password && (
              <div className="auth-page-field-error">{errors.password}</div>
            )}
          </Col>
        </Row>
      </Form.Group>

      {errorMessage && (
        <div className="auth-page-field-error">{errorMessage}</div>
      )}
      <Button
        variant="primary"
        className={`auth-page-btn ${
          errors.fullName || errors.password || errors.email
            ? "auth-page-btn-disabled"
            : "auth-page-btn-success"
        }`}
        type="submit"
      >
        Sign In
      </Button>
    </Form>
  );
}

export default SignInForm;
