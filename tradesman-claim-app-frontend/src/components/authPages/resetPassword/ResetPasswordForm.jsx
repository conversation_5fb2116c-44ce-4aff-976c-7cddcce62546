import React, { useState, useEffect } from "react";
import { Form, Button, Row, Col, InputGroup } from "react-bootstrap";
import { useFormik } from "formik";
import { resetPasswordSchema } from "../../../utils/resetValidation";
import { updateUserInfo } from "../../../services/updateProfileInfo";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { Link } from "react-router-dom";

function ResetPasswordForm({ setOpen, token }) {
  const [showPassword, setShowPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState();
  const [sucussfullyReset, setSucussfullyReset] = useState(false);
  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const initialValues = {
    password: "",
    passwordConfirmation: "",
  };

  const { values, handleChange, handleBlur, handleSubmit, errors, touched } =
    useFormik({
      initialValues,
      validationSchema: resetPasswordSchema,
      validateOnChange: true,
      validateOnBlur: false,
      onSubmit: async (values, action) => {
        setOpen(true);
        let payload = {
          password: values.password,
          password_confirmation: values.passwordConfirmation,
          token: token,
        };
        const response = await updateUserInfo.resetPassword(payload);
        setOpen(false);
        if (response.status === "Success") {
          setShowPassword(false);
          action.resetForm();
          handleResetPassword();
          setSucussfullyReset(true);
        } else {
          setErrorMessage(response.messages[0]);
        }
      },
    });

  const handleResetPassword = () => {
    setSucussfullyReset(true);
  };
  return (
    <>
      {sucussfullyReset ? (
        <Col>
          <div className="d-flex text-center align-items-center justify-content-center h-100">
            <div>
              <h3 className="auth-page-title">Password has changed</h3>
              <div className="auth-page-subtitle">
                Your password has successfully changed. Please Sign in to your
                account
              </div>
              <Link to="/login" className="have-account-link">
                <Button
                  variant="primary"
                  className={`auth-page-btn ${
                    errors.password || errors.passwordConfirmation
                      ? "auth-page-btn-disabled"
                      : "auth-page-btn-success"
                  }`}
                  type="submit"
                >
                  Sign In
                </Button>
              </Link>
            </div>
          </div>
        </Col>
      ) : (
        <Form onSubmit={handleSubmit} className="w-100">
          <Row>
            <h3 className="auth-page-title">Reset Password</h3>
            <div className="auth-page-subtitle">
              Please enter your new password
            </div>
          </Row>
          <Row></Row>
          <Form.Group>
            <Row>
              <Col className="mb-2">
                <div className="d-flex justify-content-between">
                  <Form.Label className="ui-button-s">Password</Form.Label>
                </div>
                <InputGroup className="auth-page-pwd-field">
                  <Form.Control
                    type={showPassword ? "text" : "password"}
                    autoComplete="off"
                    name="password"
                    id="password"
                    placeholder="Enter password"
                    value={values.password}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={
                      errors.password && touched.password
                        ? "auth-page-field-with-error"
                        : null
                    }
                  />
                  <span
                    onClick={toggleShowPassword}
                    className="pwd-field-icon-container"
                  >
                    {showPassword ? (
                      <FaEyeSlash className="pwd-field-icon" />
                    ) : (
                      <FaEye className="pwd-field-icon" />
                    )}
                  </span>
                </InputGroup>
                {errors.password && touched.password && (
                  <div className="auth-page-field-error">{errors.password}</div>
                )}
                <Form.Label className="ui-button-s mt-3">
                  Confirm Password
                </Form.Label>
                <Form.Control
                  type="password"
                  autoComplete="off"
                  name="passwordConfirmation"
                  id="passwordConfirmation"
                  placeholder="Repeat password"
                  value={values.passwordConfirmation}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  className={
                    errors.passwordConfirmation && touched.passwordConfirmation
                      ? "auth-page-field-with-error"
                      : null
                  }
                />
                {errors.passwordConfirmation &&
                  touched.passwordConfirmation && (
                    <div className="auth-page-field-error">
                      {errors.passwordConfirmation}
                    </div>
                  )}
              </Col>
            </Row>
          </Form.Group>
          {errorMessage && (
            <div className="auth-page-field-error">{errorMessage}</div>
          )}

          <Button
            variant="primary"
            className={`auth-page-btn ${
              errors.password || errors.passwordConfirmation
                ? "auth-page-btn-disabled"
                : "auth-page-btn-success"
            }`}
            type="submit"
          >
            Update Password
          </Button>
        </Form>
      )}
    </>
  );
}

export default ResetPasswordForm;
