import React, { useState, useEffect } from "react";
import AuthComponentWrapper from "../authComponentWrapper/AuthComponentWrapper";
import "../style.css";
import Loader from "../../loader/Loader";
import ResetPasswordForm from "./ResetPasswordForm";

function ResetPassword() {
  const [token, setToken] = useState("");
  const [open, setOpen] = useState(false);

  useEffect(() => {
    const fullPath = window.location.href;
    const tokenPart = fullPath.split("/resetpassword/")[1];
    if (tokenPart) {
      setToken(tokenPart);
    }
  }, []);

  return (
    <AuthComponentWrapper>
      <Loader open={open} />
      <ResetPasswordForm setOpen={setOpen} token={token} />
    </AuthComponentWrapper>
  );
}

export default ResetPassword;
