import React, { useState } from "react";
import "../style.css";
import Loader from "../../loader/Loader";
import Logo from "../../../assets/images/logo.png";
import { Container, Col } from "react-bootstrap";

function AuthComponentWrapper({ children }) {
    //eslint-disable-next-line
    const [open, setOpen] = useState(false);
    return (
        <Container
            fluid
            className="d-flex flex-column vh-100 align-items-center justify-content-center">
            <img src={Logo} alt="logo" className="logo" />
            <Col xs={12} sm={9} md={6} lg={5} xl={4} xxl={3} className="d-flex flex-column align-items-center">
                <Loader open={open} />
                {children}
            </Col>
        </Container>
    );
}

export default AuthComponentWrapper;

