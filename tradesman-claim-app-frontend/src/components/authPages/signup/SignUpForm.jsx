import React, { useState, useEffect } from "react";
import { Form, Button, Row, Col, InputGroup } from "react-bootstrap";
import { useNavigate } from "react-router";
import { useFormik } from "formik";
import { signUpSchema } from "../../../utils/signUpValidation";
import { serviceCall } from "../../../services/signUp";
import { Link } from "react-router-dom";
import { FaEye, FaEyeSlash } from "react-icons/fa";

function SignUpForm({ setOpen }) {
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const [errorMessage, setErrorMessage] = useState();
  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const initialValues = {
    fullName: "",
    email: "",
    password: "",
  };

  const { values, handleChange, handleBlur, handleSubmit, errors, touched } =
    useFormik({
      initialValues,
      validationSchema: signUpSchema,
      validateOnChange: true,
      validateOnBlur: false,
      onSubmit: async (values, action) => {
        setOpen(true);
        let payload = {
          name: values.fullName,
          email: values.email,
          password: values.password,
        };
        const response = await serviceCall.signUp("/auth/signup", payload);
        setOpen(false);
        if (response.status === "Success") {
          navigate("/claims", {
            state: { navigationFlag: true },
          });
        } else {
          setErrorMessage(response.messages);
        }
      },
    });

  return (
    <Form onSubmit={handleSubmit} className="w-100">
      <Row>
        <h3 className="auth-page-title"> Create an account</h3>
      </Row>
      <Row>
        <Col className="mb-2">
          <Form.Label className="ui-button-s">Name*</Form.Label>
          <Form.Control
            type="text"
            name="fullName"
            id="fullName"
            placeholder="Enter your name"
            value={values.fullName}
            onChange={handleChange}
            onBlur={handleBlur}
            className={
              errors.fullName && touched.fullName
                ? "auth-page-field-with-error"
                : null
            }
          />
          {errors.fullName && touched.fullName && (
            <div className="auth-page-field-error">{errors.fullName}</div>
          )}
        </Col>
      </Row>
      <Form.Group>
        <Row>
          <Col className="mb-2">
            <Form.Label className="ui-button-s">Email*</Form.Label>
            <Form.Control
              type="email"
              name="email"
              id="email"
              placeholder="Enter your email"
              value={values.email}
              onChange={handleChange}
              onBlur={handleBlur}
              className={
                errors.email && touched.email
                  ? "auth-page-field-with-error"
                  : null
              }
            />
            {errors.email && touched.email && (
              <div className="auth-page-field-error">{errors.email}</div>
            )}
          </Col>
        </Row>
        <Row>
          <Col className="mb-2">
            <div className="d-flex justify-content-between">
              <Form.Label className="ui-button-s">Password*</Form.Label>
              <Link to="/forgotpassword" className="have-account-link">
                <p className="forgot-pwd-btn">Forgot password?</p>
              </Link>
            </div>
            <InputGroup className="auth-page-pwd-field">
              <Form.Control
                type={showPassword ? "text" : "password"}
                autoComplete="off"
                name="password"
                id="password"
                placeholder="Enter password"
                value={values.password}
                onChange={handleChange}
                onBlur={handleBlur}
                className={
                  errors.password && touched.password
                    ? "auth-page-field-with-error"
                    : null
                }
              />
              <span
                onClick={toggleShowPassword}
                className="pwd-field-icon-container"
              >
                {showPassword ? (
                  <FaEyeSlash className="pwd-field-icon" />
                ) : (
                  <FaEye className="pwd-field-icon" />
                )}
              </span>
            </InputGroup>
            {errors.password && touched.password && (
              <div className="auth-page-field-error">{errors.password}</div>
            )}
          </Col>
        </Row>
      </Form.Group>

      {errorMessage && (
        <div className="auth-page-field-error">{errorMessage}</div>
      )}
      <Button
        variant="primary"
        className={`auth-page-btn ${
          errors.fullName || errors.password || errors.email
            ? "auth-page-btn-disabled"
            : "auth-page-btn-success"
        }`}
        type="submit"
      >
        Sign Up
      </Button>
    </Form>
  );
}

export default SignUpForm;
