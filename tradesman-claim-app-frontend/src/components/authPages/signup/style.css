/* .signin-nav-btn {
  width: 75px;
  height: 32px;
  background: #FFFFFF;
  border: 1px solid #DADDE6;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  cursor: pointer;
}

.signup-form-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 0px;
  gap: 24px;

  position: absolute;
  width: 408px;
  left: 600px;
  top: 100px;
}

.singup-title {
  margin-left: 5px;
  font-weight: 500;
  font-size: 32px;
  line-height: 40px;
  letter-spacing: -0.02em;
  color: #000117;
}

.row .signup-field {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  font-size: 14px;
  gap: 8px;
  height: 40px;
  background: #F6F8FC;
  border: 1px solid #DADDE5;
  border-radius: 8px;
}

.row .signup-pwd-field {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 0px;
  font-size: 14px;
  gap: 0px;
  height: 40px;
  background: #F6F8FC;
  border: 1px solid #DADDE5;
  border-radius: 8px;

}

.row .signup-name-filed {
  background: #F6F8FC;
  border: 1px solid #DADDE5;
  border-radius: 8px;
  font-size: 14px;
  height: 45px;
}

.row .signup-dropdown-field {
  background-color: #F6F8FC;
  border: 1px solid #DADDE5;
  color: #6c757d;
  font-size: 14px;
  padding: 5px;
  height: 45px;
  border-radius: 8px
}

.row .pwd-field-bg {
  background: #F6F8FC;
  border: 1px solid #DADDE5;
  border-left: 0px;
  border-right: 0px;
}

.row .signup-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 20px;
  gap: 8px;
  width: 405px;
  height: 48px;
  background: #000117;
  border-radius: 8px;
}

.row .signup-btn:hover {
  background: #000117;
}

.error-text {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 0.02em;
  color: #EF0C0C;
}

.succuss-text {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 0.02em;
  color: black;
}

.row .pwd-error-icon {
  color: green;
}

.sidebar-wrapper {
  padding: 0;
  height: 100vh;
}

span.pwd-field-bg.input-group-text {
  cursor: pointer;
} */