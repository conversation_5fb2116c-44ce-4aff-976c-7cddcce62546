import React, { useState } from "react";
import AuthComponentWrapper from "../authComponentWrapper/AuthComponentWrapper";
import "../style.css";
import Loader from "../../loader/Loader";
import SignUpForm from "./SignUpForm";
import { Link } from "react-router-dom";

function SignUp() {
    const [open, setOpen] = useState(false);
    return (
        <AuthComponentWrapper>
            <Loader open={open} />
            <SignUpForm setOpen={setOpen} />
            <div className="text-center mt-4">
                <p className="ui-button-l">
                    Already have an account? <Link to="/signin">Sign In</Link>
                </p>
            </div>
        </AuthComponentWrapper>
    );
}

export default SignUp;
