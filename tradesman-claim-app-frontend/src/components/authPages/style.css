.logo {
  width: 248px;
}

.auth-page-title {
  text-align: center;
  font-style: normal;
  margin: 0 0 12px 0;
}

.auth-page-subtitle {
  color: #4b5565;
  text-align: center;
  margin: 0 0 12px 0;
}

.auth-page-field-container {
  margin-bottom: 12px;
}

.auth-page-field-error::first-letter {
  text-transform: capitalize;
}

.forgot-pwd-btn {
  font-size: 14px;
  font-weight: 500 !important;
  line-height: 22px;
  color: #099250;
  margin: 0;
}

.auth-page-form-container {
  /* width: 464px; */
}

.auth-page-btn {
  margin-top: 20px;
  width: 100%;
  height: 48px;
  color: #fff !important;
  background: #099250 !important;
  border-color: #099250 !important;
  border-radius: 8px;
  box-shadow: 0px 1px 2px 0px #1018280d !important;
}

.auth-page-form-container .auth-page-btn:hover,
.auth-page-form-container .auth-page-btn:focus,
.auth-page-form-container .auth-page-btn:active,
.auth-page-form-container .auth-page-btn-success {
  background-color: #099250 !important;
  border-color: #099250 !important;
  color: #fff !important;
}

.auth-page-btn-disabled {
  pointer-events: none;
  color: #9aa4b2 !important;
  background: #e3e8ef !important;
  border-color: #e3e8ef !important;
}

.admin-row-btn:disabled {
  background-color: #939493;
}

#auth-page-btn-delete {
  background-color: #ffffff !important;
  border: 1px solid #d92d20 !important;
  color: #d92d20 !important;
}

.auth-page-field-with-error {
  border-color: rgb(240, 68, 56) !important;
  box-shadow: 2px 1px 2px rgba(240, 68, 56, 0.05) !important;
}

.auth-page-field-error {
  font-size: 14px;
  margin: 2px 0 0 5px;
  color: #f04438;
}

.auth-page-field-with-error:focus {
  border-color: #f04438 !important;
  box-shadow: 0px 0px 0px 4px #feecec !important;
}

.auth-page-pwd-field {
  position: relative;
}

.auth-page-pwd-field .pwd-field-icon {
  position: absolute;
  top: 25%;
  right: 10px;
  cursor: pointer;
  z-index: 999;
}

.btn-forgot-password-resend-signin {
  background-color: #f8f9fa !important;
  border-radius: 8px !important;
  color: #344054 !important;
  border-color: #d0d5dd !important;
  width: 200px;
  padding: 12px 20px !important;
}
.btn-forgot-password-resend-signin:hover {
  background-color: #099250 !important;
  border-color: #099250 !important;
  color: #ffffff !important;
}
