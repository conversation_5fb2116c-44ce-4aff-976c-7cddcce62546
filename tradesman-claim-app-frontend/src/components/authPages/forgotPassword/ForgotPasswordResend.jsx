import React from "react";
import AuthComponentWrapper from "../authComponentWrapper/AuthComponentWrapper";
import "../style.css";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "react-bootstrap";

function ForgotPasswordResend() {
    return (
        <AuthComponentWrapper>
            <h3 className="auth-page-title">Forgot Password?</h3>
            <div className="auth-page-subtitle">
                We sent a password reset link to your email. Please check your inbox and spam folders.
            </div>
            <div className="text-center mt-4">
                <p className="ui-button-l">
                    Didn't receive an email? <Link to='/forgotpassword'>Resend</Link>
                </p>
                <Link to='/signin'>
                    <Button variant="primary" className="ui-button-m btn-forgot-password-resend-signin">
                        Back to Sign in
                    </Button>
                </Link>
            </div>
        </AuthComponentWrapper>
    );
}

export default ForgotPasswordResend;
