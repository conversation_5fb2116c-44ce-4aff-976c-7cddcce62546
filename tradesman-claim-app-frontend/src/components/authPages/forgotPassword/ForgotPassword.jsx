import React, { useState } from "react";
import AuthComponentWrapper from "../authComponentWrapper/AuthComponentWrapper";
import "../style.css";
import Loader from "../../loader/Loader";
import ForgotPasswordForm from "./ForgotPasswordForm";

function ForgotPassword() {
    const [open, setOpen] = useState(false);
    return (
        <AuthComponentWrapper>
            <Loader open={open} />
            <ForgotPasswordForm setOpen={setOpen} />
        </AuthComponentWrapper>
    );
}

export default ForgotPassword;
