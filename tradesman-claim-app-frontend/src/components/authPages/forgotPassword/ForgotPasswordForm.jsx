import React, { useEffect, useState } from "react";
import { Form, Button, Row, Col } from "react-bootstrap";
import { serviceCall } from "../../../services/forgotPassword";
import { forgotPasswordSchema } from "../../../utils/forgotPasswordValidation";
import { useFormik } from "formik";
import { useNavigate } from "react-router-dom";

function ForgotPasswordForm({ setOpen }) {
  const navigate = useNavigate();
  const [errorMessage, setErrorMessage] = useState("");

  const initialValues = {
    email: "",
  };

  const { values, handleBlur, handleChange, handleSubmit, errors, touched } =
    useFormik({
      initialValues,
      validationSchema: forgotPasswordSchema,
      validateOnChange: true,
      validateOnBlur: false,
      onSubmit: async (values, action) => {
        setOpen(true);
        const response = await serviceCall.forgotPassword(values);
        setOpen(false);
        if (response.status === "Success") {
          navigate("/forgotpasswordresend", {
            state: { navigationFlag: true, email: values.email },
          });
        } else {
          action.resetForm();
          setErrorMessage(response.messages[0]);
        }
      },
    });

  return (
    <Form onSubmit={handleSubmit} className="w-100">
      <h3 className="auth-page-title">Forgot Password?</h3>
      <div className="auth-page-subtitle">
        Enter the email address you used for signing up and we will send you the
        link to reset your password.
      </div>

      <Form.Group>
        <Row>
          <Col className="mb-2">
            <Form.Label className="ui-button-s">Email</Form.Label>
            <Form.Control
              type="email"
              name="email"
              id="email"
              placeholder="Enter your email"
              value={values.email}
              onChange={handleChange}
              onBlur={handleBlur}
              className={
                errors.email && touched.email
                  ? "auth-page-field-with-error"
                  : null
              }
            />
            {errors.email && touched.email && (
              <div className="auth-page-field-error">{errors.email}</div>
            )}
          </Col>
        </Row>
      </Form.Group>

      {errorMessage && (
        <div className="auth-page-field-error">{errorMessage}</div>
      )}
      <Button
        variant="primary"
        className={`auth-page-btn ${
          errors.email ? "auth-page-btn-disabled" : "auth-page-btn-success"
        }`}
        type="submit"
      >
        Reset Password
      </Button>
    </Form>
  );
}

export default ForgotPasswordForm;
