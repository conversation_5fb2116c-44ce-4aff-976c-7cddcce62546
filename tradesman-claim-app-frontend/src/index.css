@import url("https://fonts.googleapis.com/css4?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");

body {
  font-family: "Inter" !important;
  margin: 0;
  background: #fcfcfd !important;
}

button,
textarea {
  font-family: "Inter" !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  line-height: 24px !important;
}

textarea:active,
textarea:focus {
  border-color: #099250 !important;
  border-width: 1px !important;
  box-shadow: 0px 0px 0px 2px #d3f8df !important;
}

body h1 {
  font-style: normal;
  font-weight: 700;
  font-size: 48px;
  /* Adjust as needed */
  line-height: calc(48px + 0.5rem);
  /* (font-size) + 0.5rem */
  color: #002;
}

body h2 {
  font-style: normal;
  font-weight: 700;
  font-size: 40px;
  /* Adjust as needed */
  line-height: calc(40px + 0.5rem);
  /* (font-size) + 0.5rem */
  color: #002;
}

body h3 {
  font-style: normal;
  font-weight: 700;
  font-size: 32px;
  /* Adjust as needed */
  line-height: 40px;
  /* Adjust as needed */
  color: #002;
}

body h4 {
  font-style: normal;
  font-weight: 700;
  font-size: 24px;
  /* Adjust as needed */
  line-height: 32px;
  /* Adjust as needed */
  color: #101828;
}

body h5 {
  font-style: normal;
  font-weight: 700;
  font-size: 24px;
  /* Adjust as needed */
  line-height: 32px;
  /* Adjust as needed */
  color: #002;
}

body h6 {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  /* Adjust as needed */
  line-height: 28px;
  /* Adjust as needed */
  color: #002;
}

body p {
  font-family: "Inter";
  font-style: normal;
  font-size: 16px;
  line-height: calc(16px + 0.5rem);
  font-weight: regular;
}

body p.paragraph-l {
  font-size: 18px;
  line-height: calc(18px + 0.5rem);
  font-weight: regular;
}

body p.paragraph-m {
  font-size: 16px;
  line-height: calc(16px + 0.5rem);
  font-weight: regular;
}

body p.paragraph-s {
  font-size: 14px;
  line-height: calc(14px + 0.5rem);
  font-weight: regular;
}

body p.paragraph-xs {
  font-size: 12px;
  line-height: calc(12px + 0.5rem);
  font-weight: regular;
}

.ui-button-l {
  font-size: 18px;
  line-height: calc(18px + 0.5rem);
  font-weight: 500;
}

.ui-button-m {
  font-size: 16px;
  line-height: calc(16px + 0.5rem);
  font-weight: 500;
}

.ui-button-s {
  font-size: 14px;
  line-height: calc(14px + 0.5rem);
  font-weight: 500;
}

.ui-overline {
  font-size: 10px;
  line-height: calc(10px + 0.5rem);
  letter-spacing: 15%;
  font-weight: bold;
}

.img-holder,
img {
  width: 100%;
  height: auto;
}

#navbar {
  height: 80px;
  align-items: flex-end;
}

.navbar-brand img {
  width: 164px;
  height: auto;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

.signin-form-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 2px;
  gap: 14px;
  position: absolute;
  right: 34px;
  top: 29px;
}

.have-account-text {
  font-weight: 402;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: 2.02em;
  color: #000002;
  margin: 2;
}

.create-account-btn {
  width: 162px;
  height: 34px;
  background: #ffffff;
  border: 3px solid #dadde6;
  box-shadow: 2px 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

.cancel-btn {
  margin-top: 20px;
  width: 100%;
  height: 48px;
  background: #ffffff !important;
  border: 1px solid #d0d5dd !important;
  border-radius: 8px;
  color: #000117 !important;
}

.have-account-link {
  text-decoration: none;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  display: none;
}

.success-img {
  width: 58px;
}

.dark-color {
  color: #000119 !important;
}

.green {
  color: #171c00 !important;
}

.pointer {
  cursor: pointer;
}

input {
  padding: 10px 14px 10px 14px;
  gap: 8px;
  border: 1px solid #cdd5df;
}

input:focus,
input:active {
  border-color: #099250 !important;
  /* Green border */
  border-width: 1px !important;
  /* Thinner border on focus or active */
  box-shadow: 0px 0px 0px 2px #d3f8df !important;
  /* Adjust the box-shadow thickness */
}

.form-select {
  padding: 10px 14px 10px 14px;
  gap: 8px;
  border: 1px solid #cdd5df !important;
}

.form-select:focus {
  border-color: #099250 !important;
  border-width: 1px !important;
  box-shadow: 0px 0px 0px 4px #d3f8df !important;
}

.not-found-title {
  font-size: 56px;
  line-height: 74px;
}

.h-full {
  height: 102vh;
}

a {
  text-decoration: none !important;
  color: #099252 !important;
}

.close-icon-wrapper {
  float: right;
  cursor: pointer;
  background: #ffffff;
}

.close-icon {
  width: 24px !important;
  height: 24px !important;
  color: #667085;
}

.form-control {
  font-family: Inter !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  line-height: 24px !important;
  text-align: left !important;
  color: #121926 !important;
}

.form-control::placeholder {
  color: #9aa4b2 !important;
}

.tab-content .form-select.select-placeholder-text {
  font-family: Inter !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 24px !important;
  text-align: left !important;
  color: #9aa4b2;
}

.tab-content .form-select option {
  font-family: Inter !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 24px !important;
  text-align: left !important;
  color: #000117;
}

.tab-content .form-select .select-placeholder-text {
  font-family: Inter !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 24px !important;
  text-align: left !important;
  color: #9aa4b2 !important;
}

.tab-content .MuiInputBase-root {
}

.tab-content .MuiInputBase-root:focus {
  border: none !important;
  box-shadow: 0px 0px 0px 4px #d3f8df !important;
}

.tab-content .MuiInputBase-root:active {
  border: none !important;
  box-shadow: 0px 0px 0px 4px #d3f8df !important;
}

.tab-content .MuiInputBase-root:hover {
  /* border: 1px solid #099250 !important; */
  box-shadow: 0px 0px 0px 4px #d3f8df !important;
}

.tab-content .MuiOutlinedInput-notchedOutline {
  border-radius: 8px;
  border: 1px solid #cdd5df;
}

.tab-content .MuiOutlinedInput-notchedOutline:hover,
.tab-content .MuiOutlinedInput-notchedOutline:focus {
  border: 1px solid #099250;
  box-shadow: 0px 0px 0px 4px #d3f8df !important;
}

.tab-content .MuiOutlinedInput-notchedOutline {
  border-radius: 8px;
  border: 1px solid #cdd5df;
}

.tab-content .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline,
.tab-content .MuiOutlinedInput-root:focus .MuiOutlinedInput-notchedOutline,
.tab-content .MuiOutlinedInput-root:active .MuiOutlinedInput-notchedOutline {
  border: 1px solid #099250 !important;
  /* Green border on focus or hover */
  box-shadow: 0px 0px 0px 2px #d3f8df !important;
  /* Adjust box-shadow thickness */
}

.modal-dialog {
  min-width: 600px;
}

@media (max-width: 768px) {
  .modal-dialog {
    min-width: 90vw !important;
    width: 100% !important;
    margin: 15% auto !important;
    display: flex !important;
    justify-content: center !important;
  }

  .modal-content {
    width: 90vw !important;
    align-self: center !important;
  }
}

.modal-content {
  border-radius: 12px !important;
  box-shadow: 0px 8px 8px -4px #10182808 !important;
  box-shadow: 0px 20px 24px -4px #10182814 !important;
}

.select-container {
  padding-right: 1.25rem !important;
}

.custom-radio-buttons {
  display: flex;
  gap: 6px;
  width: 100%;
}

.custom-radio {
  flex: 1;
}

.custom-radio input[type="radio"] {
  display: none;
}

.custom-radio label {
  display: flex;
  height: 52px;
  align-items: center;
  width: 100%;
  padding: 16px;
  background: #ffffff;
  border: 1px solid #d0d5dd;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #4b5565;
  cursor: pointer;
  transition: all 0.3s ease;
}

.custom-radio input[type="radio"]:checked + label {
  background-color: #f0fdf9;
  border-color: #099250;
  color: #121926;
}

.custom-radio label:hover {
  background-color: #f9fafb;
}

.check-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 16px;
  height: 18px;
  background-color: #ffffff;
  border: 1px solid #d0d5dd;
  border-radius: 4px;
  margin-right: 8px;
}

.custom-radio input[type="radio"]:checked + label .check-icon-wrapper {
  background-color: #099250;
  border-color: #12b76a;
  color: #ffffff !important;
}

.check-icon {
  color: #ffffff !important;
  font-size: 12px;
}

.custom-radio label span:last-child {
  flex-grow: 1;
}

#users-tab .css-1419s9i-MuiTableRow-root td:last-child,
#companies-tab .css-1419s9i-MuiTableRow-root td:last-child {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
