import React from "react";
import { Route, Routes } from "react-router-dom";
import SignIn from "./components/authPages/signIn/SignIn";
import SignUp from "./components/authPages/signup/SignUp";
import ForgotPassword from "./components/authPages/forgotPassword/ForgotPassword";
import ForgotPasswordResend from "./components/authPages/forgotPassword/ForgotPasswordResend";
import ResetPassword from "./components/authPages/resetPassword/ResetPassword";
import Claims from "./pages/claims/Claims";
import UnassignedClaims from "./pages/unassignedClaims/UnassignedClaims";
import Users from "./pages/users/Users";
import Companies from "./pages/companies/Companies";
import Auth from "./utils/Auth";
import PageNotFound from "./pages/pageNotFound/PageNotFound";

function App() {
  return (
    <Routes>
      <Route path="/signup" element={<SignUp />} />
      <Route path="/signin" element={<SignIn />} />
      <Route path="/forgotpassword" element={<ForgotPassword />} />
      <Route path="/forgotpasswordresend" element={<ForgotPasswordResend />} />
      <Route path="/resetpassword/*" element={<ResetPassword />} />

      <Route element={<Auth />}>
        <Route path="/claims" element={<Claims />} />
        <Route path="/unassigned-claims" element={<UnassignedClaims />} />
        <Route path="/users" element={<Users />} />
        <Route path="/law-firms" element={<Companies />} />
        <Route path="/*" element={<Claims />} />
      </Route>
      <Route path="*" element={<PageNotFound />} />
    </Routes>
  );
}

export default App;
