import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  token: null,
  userDetails: {
    id: null,
    name: null,
    role: null,
    permissions: [],
    companyId: null,
    companyName: null,
  },
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setAuthData: (state, action) => {
      const {
        token,
        user_name,
        user_role,
        user_permissions,
        user_id,
        company_id,
        company_name,
      } = action.payload;

      // Set token
      state.token = token;

      // Set userDetails object
      state.userDetails = {
        id: user_id,
        name: user_name,
        role: user_role,
        companyId: company_id,
        companyName: company_name,
        // Only update permissions if they're provided and not empty
        permissions:
          user_permissions?.length > 0
            ? user_permissions
            : state.userDetails.permissions,
      };
    },
    clearAuth: (state) => {
      return initialState;
    },
  },
});

export const { setAuthData, clearAuth } = authSlice.actions;

// Updated selectors with new name
export const selectAuth = (state) => state.auth;
export const selectUserDetails = (state) => state.auth.userDetails;
export const selectPermissions = (state) => state.auth.userDetails.permissions;
export const selectUserRole = (state) => state.auth.userDetails.role;
export const selectCompanyId = (state) => state.auth.userDetails.companyId;

export default authSlice.reducer;
