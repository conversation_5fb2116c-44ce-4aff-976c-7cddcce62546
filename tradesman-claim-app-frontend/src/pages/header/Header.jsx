import React from "react";
import { Navbar, Container, Nav, <PERSON><PERSON> } from "react-bootstrap";
import logo from "../../assets/images/logo.png";
import { updateUserInfo } from "../../services/updateProfileInfo";
import { handleCookies } from "../../utils/cookies";
import { useNavigate } from "react-router-dom";
import "./style.css";

import { FaSignOutAlt } from "react-icons/fa";

function Header() {
  const navigate = useNavigate();
  const activeLink = window.location.pathname.split("/")[1];

  const currentUserData = handleCookies.fetchCookies();

  const handleLogout = async () => {
    const response = await updateUserInfo.signOut();
    if (response.status === "Success") {
      handleCookies.deleteCookies();
      window.location.href = "/signin";
    }
  };

  return (
    <>
      {" "}
      <header className="header-background">
        <Container className="p-0">
          <Navbar className="navbar" id="navbar">
            <Container className="p-o m-0 nav-mobile-container-header">
              <Navbar.Brand
                className="brand-mobile-container"
                onClick={() => navigate("/")}
              >
                <img alt="" src={logo} />
              </Navbar.Brand>
            </Container>
            <Container
              className="justify-content-between"
              id="center-nav-links"
            >
              <Nav.Link
                className={`nav-link ${activeLink == "claims" || activeLink == "unassigned-claims" ? "active" : ""}`}
                href="/claims"
              >
                Insurance Claims
              </Nav.Link>
              {currentUserData.user_role === "Admin" ? (
                <Nav.Link
                  className={`nav-link ${activeLink === "users" ? "active" : ""}`}
                  href="/users"
                >
                  Users
                </Nav.Link>
              ) : null}
              {currentUserData.user_role === "Admin" ? (
                <Nav.Link
                  className={`nav-link ${activeLink === "law-firms" ? "active" : ""}`}
                  href="/law-firms"
                >
                  Law Firms
                </Nav.Link>
              ) : null}
            </Container>
            <Container className="justify-content-end">
              <Nav.Link className="px-3" onClick={() => handleLogout()}>
                <Button
                  variant="outlined"
                  id="logout-btn"
                  onClick={handleLogout}
                >
                  Logout
                  <FaSignOutAlt />
                </Button>
              </Nav.Link>
            </Container>
          </Navbar>
        </Container>
      </header>{" "}
    </>
  );
}

export default Header;
