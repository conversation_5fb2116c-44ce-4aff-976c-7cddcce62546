.header-background {
    background-color: #FFFFFF;
    border-bottom: 1px solid #CDD5DF;
}

#logout-btn {
    border-radius: 8px;
    border-color: #CDD5DF;
    width: 114px;
    padding: 11px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    line-height: 24px;
}

#logout-btn svg {
    width: 20px;
    height: 20px;
}

#logout-btn:hover {
    background-color: #F8F9FA;
    border-color: #CDD5DF;
}

.navbar-nav .nav-link {
    display: flex;
    color: #ffffff;
    font-weight: 400;
}

#center-nav-links {
    max-width: 300px;
    margin-bottom: -0.5rem;
    justify-content: space-around !important;
}

#center-nav-links .nav-link {
    font-size: 14px !important;
    line-height: 20px !important;
    color: #121926 !important;
    padding-bottom: 25px;
}

#center-nav-links .nav-link:active {
    color: #099250 !important;
}

#center-nav-links .nav-link.active {
    color: #099250 !important;
    border-bottom: 2px solid #099250;
}

.navbar-nav .nav-link:hover {
    color: #ffffff;
}

.custom-dropdown-menu[data-bs-popper] {
    top: 100% !important;
    left: -35px !important;
    margin-top: var(--bs-dropdown-spacer) !important;
}

.user-image img,
p.header-username {
    border-radius: 50%;
    max-width: 40px;
    height: 40px;
    font-size: 16px;
}

.navbar-nav .header-button {
    display: flex;
    height: 40px;
    padding: 0px 12px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    border: none;
    outline: none;
    background: linear-gradient(90deg, #6206b8 0%, #00b6d6 100%);
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

.img-btn {
    width: 18px;
    height: 20px;
}

.btn-text {
    color: var(--text-on-black-high-contrast, #fff);
    font-family: Ubuntu;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    /* 114.286% */
    letter-spacing: 0.28px;
}

.lightning-image {
    width: 20px;
    height: 20px;
}

.active-linkA {
    display: flex;
    padding: 8px 12px;
    align-items: center;
    gap: 105px;
    color: #ffffff;
    border-radius: 6px;
    background: var(--shades-desaturate-3, #1e253a);
}

.nav-line-item-margin {
    margin-right: 16px;
}

.nav-container {
    display: flex;
    align-items: center;
}

.mobile-nav-icons-conatiner {
    width: 100%;
    display: flex;
    align-items: center;
    padding-top: 8px;
    /* padding-bottom: 8px ; */
    /* margin-bottom: 4px ; */
    gap: 16px;
    min-height: 48px;
}

.mobile-nav-text {
    width: 100%;
    color: var(--text-on-black-high-contrast, #fff);
    min-width: 124px;
    width: 100%;
    font-family: Ubuntu;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    /* 142.857% */
    color: rgba(255, 255, 255, 1);
}

.nav-icon {
    height: 20px;
    width: 20px;
}

.close-icons {
    height: 40px;
    width: 40px;
    margin-left: 12px;
    margin-right: 16px;
}

/* .brand-mobile-container { */
/*   margin-left: 3%; */
/* } */
.nav-close-navigations {
    display: flex;
    justify-content: flex-end;
    width: 59%;
    gap: 4px;
    align-items: center;
    margin-right: 3px;
}

.mobile-profile {
    color: #01b5d6;
    flex: 1 0 0;
    /* Overline/Regular */
    font-family: Ubuntu;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px;
    /* 266.667% */
    letter-spacing: 0.24px;
    text-transform: uppercase;
}

.profile-conatiner {
    padding-left: 16px;
    margin-top: 16px;
}

.user-image {
    cursor: pointer;
}

.header-for-large-screens {
    display: block;
}

.header-for-small-screens {
    display: none;
}

@media (max-width: 950px) {
    .header-for-large-screens {
        display: none;
    }

    .header-background {
        min-height: 64px;
        padding: 0 4px;
    }

    .header-for-small-screens {
        display: block;
    }

    .nav-container {
        margin-top: 20px;
        width: 100%;

        align-items: left;
        justify-content: left;
    }

    .nav-container {
        width: 100%;
    }

    .mobile-nav-button {
        width: 100%;
        margin-top: 42px;
        display: flex;
        height: 64px;
        padding: 0px 12px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        align-self: stretch;
        border-radius: 8px;
        background: linear-gradient(90deg, #6206b8 0%, #00b6d6 100%);

        /* Shadow/XS */
        box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    }
}

.mobile-menu-toggle-icon {
    color: rgba(255, 255, 255, 1);
}

.navbar-collapse {
    border: none;
}

.navbar {
    background-color: transparent;

}

.navbar-toggler-icon {
    background-color: transparent;
}

.navbar-expand {
    margin: 0 auto;
    /* max-width: 1224px; */
    justify-content: space-between !important;
}

.navbar-expand-lg .navbar-toggler {
    color: transparent;
    border-color: transparent;
}

.navbar-expand-lg.navbar-dark .navbar-collapse {
    background-color: transparent;
}

.navbar-toggler {
    background-color: transparent;
}

/* Add this CSS to remove the background color of the toggle button when the menu is toggled */
.navbar-toggler:hover {
    background-color: transparent;
}

.mobile-nav-txt {
    color: var(--text-on-black-high-contrast, #fff);

    /* Paragraph/S/Medium */
    font-family: Ubuntu;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    /* 142.857% */
}

.active-link-mobile-header {
    width: 100% !important;
    display: flex;
    padding: 12px !important;
    height: 44p !important;
    align-items: center !important;
    gap: 105px !important;
    color: #ffffff !important;
    border-radius: 6px !important;
    background: var(--shades-desaturate-3, #1e253a) !important;
}

.menu-names {
    color: var(--text-on-black-high-contrast, #fff);
    font-family: Ubuntu;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
}


@media (min-width: 768px) {
    .nav-mobile-container-header {
        max-width: 935px !important;
        /* width: 100% !important; */
    }
}

@media (min-width: 576px) {
    .nav-mobile-container-header {
        max-width: 935px !important;
        /* width: 100% !important; */
    }
}

.trial-bar {
    background: linear-gradient(90deg, #6206B8 0%, #00B6D6 100%), #FFF;
    padding: 10px 0px;
    color: #FFF;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
}

.cursor-pointer {
    cursor: pointer;
}

.show.dropdown .min-auto {
    min-width: auto;
    left: 50%;
    transform: translate(-50%, 0px);
}
