import React, { useState } from 'react';
import { Container } from 'react-bootstrap';
import Header from '../header/Header';
import Companies from '../../components/companies/Companies';
import Loader from '../../components/loader/Loader';

function Dashboard() {
    const [loading, setLoading] = useState(false);

    return (
        <>
            <Header />
            <Container fluid className="main-dashboard-container">
                <Companies />
            </Container>
        </>
    )
}

export default Dashboard

