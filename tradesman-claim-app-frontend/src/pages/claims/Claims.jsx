import React from "react";
import { Container } from "react-bootstrap";
import Header from "../header/Header";
import { Tabs, Tab } from "@mui/material";
import { useNavigate, useLocation } from "react-router-dom";
import ClaimsTable from "../../components/claims/Claims";
import { useSelector } from "react-redux";

function Claims() {
  const navigate = useNavigate();
  const location = useLocation();
  const currentPath = location.pathname;

  const handleTabChange = (event, newValue) => {
    navigate(newValue);
  };

  const userDetails = useSelector((state) => state.auth.userDetails);
  const { permissions: userPermissions, role: userRole } = userDetails;

  const ClaimsTabs = () => {
    return (
      <Container>
        <Tabs
          value={currentPath}
          onChange={handleTabChange}
          indicatorColor="white"
          variant="scrollable"
          scrollButtons="auto"
          aria-label="scrollable auto tabs example"
          sx={{
            margin: "24px 0px",
            fontFamily: "Inter",
            fontSize: "14px",
            fontWeight: "600 !important",
            lineHeight: "20px",
            color: "#14B5565",
          }}
        >
          <Tab
            sx={{
              fontFamily: "Inter",
              fontSize: "14px",
              fontWeight: "600 !important",
              lineHeight: "20px",
              color: "#14B5565",
              borderRadius: "8px 0px 0px 8px",
              border: "1px solid #DDD5DF",
              padding: "10px 16px",
              textTransform: "capitalize !important",
              "&.Mui-selected": {
                backgroundColor: "#EDFCF2",
                color: "#099250",
              },
            }}
            label="All Claims"
            value="/claims"
          />
          <Tab
            sx={{
              fontFamily: "Inter",
              fontSize: "14px",
              fontWeight: "600 !important",
              lineHeight: "20px",
              textTransform: "capitalize !important",
              color: "#14B5565",
              borderRadius: "0px 8px 8px 0px",
              border: "1px solid #DDD5DF",
              padding: "10px 16px",
              "&.Mui-selected": {
                backgroundColor: "#EDFCF2",
                color: "#099250",
              },
            }}
            label="Unassigned Claims"
            value="/unassigned-claims"
          />
        </Tabs>
      </Container>
    );
  };

  return (
    <>
      <Header />
      <Container fluid className="main-dashboard-container">
        {userDetails.role === "Admin" ? <ClaimsTabs /> : null}
        <ClaimsTable />
      </Container>
    </>
  );
}

export default Claims;
