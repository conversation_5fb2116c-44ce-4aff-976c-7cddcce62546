import React, { useState } from 'react';
import { Container } from 'react-bootstrap';
import Header from '../header/Header';
import Users from '../../components/users/Users';
import Loader from '../../components/loader/Loader';

function Dashboard() {
    const [loading, setLoading] = useState(false);

    return (
        <>
            <Header />
            <Loader open={loading} />
            <Container fluid className="main-dashboard-container">
                <Users />
            </Container>
        </>
    );
}

export default Dashboard;
