import axios from "axios";
import { baseUrl } from "../config/constants";
import { handleCookies } from "../utils/cookies";
const { post, get } = axios;

const getToken = () => {
  const { token } = handleCookies.fetchCookies();
  return "Bearer " + token;
};

const updateDetails = async (data) => {
  try {
    const response = await post(`${baseUrl}/updateUserDetails`, data, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
    return error.message;
  }
};

const getUsers = async () => {
  try {
    const response = await fetch(
      `${process.env.REACT_APP_API_URL}/usersListing`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: getToken(),
        },
      },
    );
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error("Error fetching users:", error);
    throw error;
  }
};

const getUserDetails = async (id) => {
  try {
    const response = await get(
      `${process.env.REACT_APP_API_URL}/userDetails/`,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: getToken(),
        },
        body: JSON.stringify({ id: id }),
      },
    );
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error("Error fetching users:", error);
    throw error;
  }
};

const deleteUser = async (id) => {
  const response = await fetch(`${baseUrl}/deleteUser`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getToken(),
    },
    body: JSON.stringify({ id: id }),
  });
  return response;
};

const getRoles = async () => {
  try {
    const response = await fetch(
      `${process.env.REACT_APP_API_URL}/rolesListing`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: getToken(),
        },
      },
    );
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error("Error fetching users:", error);
    throw error;
  }
};

const updateProfileData = async (url, data) => {
  try {
    const response = await post(`${baseUrl}${url}`, data, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
    return error.message;
  }
};

export const handleUserDetails = {
  getUsers,
  getUserDetails,
  deleteUser,
  updateDetails,
  updateProfileData,
  getRoles,
};
