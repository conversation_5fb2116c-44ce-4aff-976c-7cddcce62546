import axios from "axios";
import { baseUrl } from "../config/constants";
import { handleCookies } from "../utils/cookies";
const { post, get } = axios;

const getToken = () => {
  const { token } = handleCookies.fetchCookies();
  return "Bearer " + token;
};

const updateDetails = async (data) => {
  try {
    const response = await post(`${baseUrl}/updateClaimFormDetails`, data, {
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
      },
    });
    return response.data;
  } catch (error) {
    // console.error(error);
    return {
      status: "Error",
      message: error.message,
    };
  }
};

const getUnassignedClaims = async ({
  offset = 0,
  limit = 10,
  claim_status = null,
  search = null,
} = {}) => {
  try {
    const formData = new FormData();
    formData.append("offset", offset);
    formData.append("limit", limit);

    if (claim_status) {
      formData.append("claim_status", claim_status);
    }

    if (search) {
      formData.append("search", search);
    }

    const response = await fetch(
      `${process.env.REACT_APP_API_URL}/unassignClaims`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          Authorization: getToken(),
        },
        body: formData,
      },
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching data:", error);
    throw error;
  }
};

const getClaims = async ({
  offset = 0,
  limit = 10,
  claim_status = null,
  search = null,
} = {}) => {
  try {
    const formData = new FormData();
    formData.append("offset", offset);
    formData.append("limit", limit);

    if (claim_status) {
      formData.append("claim_status", claim_status);
    }

    if (search) {
      formData.append("search", search);
    }

    const response = await fetch(
      `${process.env.REACT_APP_API_URL}/claimFormsData`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          Authorization: getToken(),
        },
        body: formData,
      },
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching data:", error);
    throw error;
  }
};

const createClaim = async (data) => {
  try {
    const response = await post(`${baseUrl}/createClaim`, data, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
    return error.message;
  }
};

const deleteClaim = async (id) => {
  const response = await fetch(`${baseUrl}/deleteClaim`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getToken(),
    },
    body: JSON.stringify({ id: id }),
  });
  return response;
};

async function assignClaims(data) {
  try {
    const response = await post(
      `${baseUrl}/assignClaimsData`,
      {
        ids: data.ids,
        firm_id: data.firm_id,
      },
      {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: getToken(),
        },
      },
    );
    return response.data;
  } catch (error) {
    console.error("Error assigning claims:", error);
    return {
      status: "Error",
      message: error.response?.data?.message || error.message,
    };
  }
}

export const handleClaimDetails = {
  assignClaims,
  getClaims,
  getUnassignedClaims,
  updateDetails,
  createClaim,
  deleteClaim,
};
