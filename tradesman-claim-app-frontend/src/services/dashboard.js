import axios from "axios";
import { baseUrl } from "../config/constants";
import { handleCookies } from "../utils/cookies";


const baseRequest = async (endpoint, data) => {
  try {
    const token = "Bearer " + await handleCookies.fetchCookies();
    const response = await axios.post(baseUrl + endpoint, data, {
      withCredentials: true,
      headers: {
        Authorization: token,
      },
    });
    return response.data;
  } catch (error) {
    return error;
  }
};

export const serviceCall = {
  getArchivedData: (data) => baseRequest("/dashboardTotalArchiveStats", data),
  getBarData: (data) => baseRequest("/dashboardServersBarChart", data),
  getDashboardListingData: (data) => baseRequest("/dashboardServersListing", data),
  getActivity: (data) => baseRequest("/dashboardActivityLogs", data),

  // server dashboard
  getServerArchivedData: (data) => baseRequest("/serversTotalArchiveStats", data),
  getServerBarData: (data) => baseRequest("/serverDatabasesBarChart", data),
  getServerDashboardListingData: (data) => baseRequest("/serverDatabasesListing", data),

  // database dashboard
  getDatabaseArchivedData: (data) => baseRequest("/databasesTotalArchiveStats", data),
  getDatabaseBarData: (data) => baseRequest("/databaseTablesBarChart", data),
  getDatabaseListingData: (data) => baseRequest("/databaseTablesListing", data),

  // table dashboard
  getTableArchivedData: (data) => baseRequest("/tablesTotalArchiveStats", data),
  getTableBarData: (data) => baseRequest("/tablesLineChart", data),
  getTableListingData: (data) => baseRequest("/tableArchivingActivities", data),
};
