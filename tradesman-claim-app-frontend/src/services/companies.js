import axios from "axios";
import { baseUrl } from "../config/constants";
import { handleCookies } from "../utils/cookies";
const { post, get } = axios;

const getToken = () => {
  const { token } = handleCookies.fetchCookies();
  return "Bearer " + token;
};

const updateDetails = async (data) => {
  try {
    const response = await post(`${baseUrl}/updateCompanyDetails`, data, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
    return error.message;
  }
};

const getCompanies = async ({ offset = 0, limit = 10, search = null } = {}) => {
  try {
    const formData = new FormData();
    formData.append("offset", offset);
    formData.append("limit", limit);

    if (search) {
      formData.append("search", search);
    }

    const response = await fetch(
      `${process.env.REACT_APP_API_URL}/companiesListing`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          Authorization: getToken(),
        },
        body: formData,
      },
    );
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error("Error fetching data:", error);
    throw error;
  }
};

const createCompany = async (data) => {
  try {
    const response = await post(`${baseUrl}/createCompany`, data, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
    return error.message;
  }
};

const deleteCompany = async (id) => {
  const response = await fetch(`${baseUrl}/deleteCompany`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getToken(),
    },
    body: JSON.stringify({ id: id }),
  });
  return response;
};

export const handleCompanyDetails = {
  getCompanies,
  updateDetails,
  createCompany,
  deleteCompany,
};
