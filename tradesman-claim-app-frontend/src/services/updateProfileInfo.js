import axios from "axios";
import { baseUrl } from "../config/constants";
import { handleCookies } from "../utils/cookies";
const { post, get } = axios;

const getToken = () => {
  const { token } = handleCookies.fetchCookies();
  return "Bearer " + token;
};

const userInfo = async () => {
  try {
    const response = await post(
      `${baseUrl}/auth/user`,
      {},
      {
        withCredentials: true,
        headers: {
          Authorization: getToken(),
        },
      },
    );

    return response.data || false;
  } catch (error) {
    return error.message;
  }
};

const updateProfileData = async (url, data) => {
  try {
    const response = await post(`${baseUrl}${url}`, data, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
    return error.message;
  }
};

const resetPassword = async (data) => {
  try {
    const response = await post(`${baseUrl}/auth/resetPassword`, data, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data.message;
  }
};

const signOut = async () => {
  try {
    const response = await axios.post(
      `${baseUrl}/logout`,
      {},
      {
        headers: {
          Authorization: getToken(),
        },
      },
    );
    return response.data;
  } catch (error) {
    if (error.response?.status === 401) {
      handleCookies.deleteCookies();
      window.location.href = "/login";
    }
    return error;
  }
};

export const updateUserInfo = {
  userInfo,
  updateProfileData,
  resetPassword,
  signOut,
};
