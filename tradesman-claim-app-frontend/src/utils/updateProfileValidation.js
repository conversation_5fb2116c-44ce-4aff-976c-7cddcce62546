import * as Yup from "yup";

export const updateProfileValidation = Yup.object({
  firstName: Yup.string().required("Input your first name"),
  lastName: Yup.string().required("Input your last name"),
  email: Yup.string().email().required("Input your email"),
  companyName: Yup.string().required("Input company name"),
  userImage: Yup.mixed().test('fileSize', 'File size must be less than 1 MB', (value) => {
    if (!value) return true;
    return value.size <= 1024 * 1024;
  }),
  countryId: Yup.number('Select Country').required("Select Country").typeError("Select Country"),
  timezoneId: Yup.number('Select TimeZone').required("Select Time Zone").typeError("Select Time Zone"),
});