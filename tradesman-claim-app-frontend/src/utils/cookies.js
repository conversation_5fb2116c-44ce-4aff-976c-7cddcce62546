import Cookies from "js-cookie";

export const handleCookies = {
  setCookies,
  fetchCookies,
  deleteCookies,
};

function setCookies(payload) {
  // Destructure permissions out so they don't get stored in cookies
  // js-cookie won't let the string get saved
  const { user_permissions, ...cookieData } = payload;

  // Only store non-permission data in cookies
  Object.entries(cookieData).forEach(([key, value]) => {
    Cookies.set(key, value);
  });

  return payload.token;
}

function fetchCookies() {
  return {
    token: Cookies.get("token"),
    user_name: Cookies.get("user_name"),
    user_role: Cookies.get("user_role"),
  };
}

function deleteCookies() {
  Cookies.remove("token");
}
