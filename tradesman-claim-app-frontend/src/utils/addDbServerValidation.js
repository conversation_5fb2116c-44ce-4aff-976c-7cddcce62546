import * as Yup from 'yup';

export const addDbServerValidation = Yup.object({
  servername: Yup.string().required('Server name is required'),
  hostname: Yup.string().required('Host name is required'),
  username: Yup.string().required('Username is required'),
  password: Yup.string().required('Password is required'),
  port: Yup.string().matches(/^[0-9]+$/, "Must be only digits").min(2, "Must be more than 1 character")
    .max(4, "Must be less than 4 characters").required('Port number is required'),
  type: Yup.number("Type").required("Select Database type").typeError("Select Database Type"),
  remoteserver: Yup.number("remoteserver").required("Select Remote Server").typeError("Select Remote Server")
});