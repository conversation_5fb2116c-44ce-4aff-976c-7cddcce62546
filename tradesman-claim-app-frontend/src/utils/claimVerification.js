import * as Yup from 'yup';

const claimValidationSchema = Yup.object().shape({
  // claimNumber: Yup.string().required('Claim number is required'),
  // claimed: Yup.number().required('Claimed amount is required').positive('Claimed amount must be positive'),
  // clientName: Yup.string().required('Client name is required'),
  // condition: Yup.string().required('Condition is required'),
  // insuranceCompany: Yup.string().required('Insurance company is required'),
  // paid: Yup.number().required('Paid amount is required').min(0, 'Paid amount must be 0 or greater'),
  // patientName: Yup.string().required('Patient name is required'),
  // policy: Yup.string().required('Policy number is required'),
  // status: Yup.string().required('Status is required'),
});

export default claimValidationSchema;
