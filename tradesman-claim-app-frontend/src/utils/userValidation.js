import * as Yup from "yup";

const userValidationSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  role_name: Yup.string().required("Role is required"),
  role_id: Yup.string().required("Role ID is required"),
  user_company: Yup.string().required("Law Firm is required"),
  company_id: Yup.string().required("Law Firm ID is required"),
});

export default userValidationSchema;
