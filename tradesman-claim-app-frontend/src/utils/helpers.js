// Converts an object with startDate and endDate properties to ISO date format
export function convertToISODate(inputObject) {
  const convertDate = (dateString) => {
    const months = {
      Jan: "01",
      Feb: "02",
      Mar: "03",
      Apr: "04",
      May: "05",
      Jun: "06",
      Jul: "07",
      Aug: "08",
      Sep: "09",
      Oct: "10",
      Nov: "11",
      Dec: "12",
    };

    const [month, day, year] = dateString.split(" ");
    return `${year}-${months[month]}-${day.slice(0, -1).padStart(2, "0")}`;
  };

  return {
    start_date: convertDate(inputObject.startDate),
    end_date: convertDate(inputObject.endDate),
  };
}

// Formats an array of date strings into a display-friendly format (e.g., "Mon, Jan 01")
export function formatDateIntoDays(inputDates) {
  const formattedDates = inputDates.map((dateString) => {
    const date = new Date(dateString);
    const dayOfWeek = date.toLocaleString("en-us", { weekday: "short" });
    const monthAndDay = date.toLocaleString("en-us", {
      month: "short",
      day: "numeric",
    });
    return `${dayOfWeek}, ${monthAndDay}`;
  });

  return formattedDates;
}

// Formats a date string into a user-friendly display format (e.g., "Today", "Yesterday", "Mon, Jan 01 2022")

export function formatDateDisplay(dateString) {
  const currentDate = new Date();
  const inputDate = new Date(dateString);

  // Check if it's today
  if (
    inputDate.getDate() === currentDate.getDate() &&
    inputDate.getMonth() === currentDate.getMonth() &&
    inputDate.getFullYear() === currentDate.getFullYear()
  ) {
    return "Today";
  }

  // Check if it's yesterday
  const yesterday = new Date(currentDate);
  yesterday.setDate(currentDate.getDate() - 1);

  if (
    inputDate.getDate() === yesterday.getDate() &&
    inputDate.getMonth() === yesterday.getMonth() &&
    inputDate.getFullYear() === yesterday.getFullYear()
  ) {
    return "Yesterday";
  }

  const date = new Date(dateString);
  const input = inputDate.getDate()
  // const dayOfWeek = date.toLocaleString("en-us", { weekday: "short" });
  const monthAndDay = date.toLocaleString("en-us", { month: "short" });
  const year = date.toLocaleString("en-us", { year: "numeric" });

  return `${monthAndDay} ${input}, ${year}`;
}

// Formats a time string into a display-friendly time format (e.g., "2:30 PM")
export function formatTimeDisplay(timeString) {
  const inputTime = new Date(timeString);
  const options = { hour: "numeric", minute: "2-digit", hour12: true };
  const formatter = new Intl.DateTimeFormat("en-US", options);
  return formatter.format(inputTime);
}

// Converts an array of date strings to an array of month names
export function convertToMonthName(dateStrings) {
  // Check if dateStrings is null or not an array
  if (!Array.isArray(dateStrings) || dateStrings.length === 0) {
    return [];
  }

  // Assuming dateStrings is an array of strings like ['2023-12', '2023-11']
  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const formattedDates = dateStrings.map((dateString) => {
    const [, month] = dateString.split("-");
    const parsedMonth = parseInt(month, 10) - 1;

    if (isNaN(parsedMonth)) {
      return null;
    }

    const monthName = monthNames[parsedMonth];
    return `${monthName}`;
  });

  return formattedDates.filter((date) => date !== null);
}

// Duplicates each value in an array and adds null values before and after each element in line chart.
export const duplicateValuesWithNulls = (values) => {
  const result = values?.reduce((acc, value, index) => {
    acc.push(value);
    if (index !== values.length - 1) {
      acc.push(value);
    }
    return acc;
  }, []);
  result.unshift(null);
  result.push(null);

  return result;
};

// Adds an empty string before and after each label in an array
export const addEmptyStrings = (labels) => {
  const result = labels?.reduce((acc, label) => {
    acc.push("");
    acc.push(label);
    return acc;
  }, []);

  result.push("");
  return result;
};

// Extracts the values from an object and returns them as an array
export const extractServerDataValues = (param) => {
  if (param && typeof param === "object") {
    return Object.values(param);
  }
  return null;
};


export function hasSingleNonEmptyString(arr) {
  var nonEmptyArray = arr.filter(function(str) {
    return str.trim() !== '';
  });

  return nonEmptyArray.length === 1;
}