import * as Yup from "yup";

export const signUpSchema = Yup.object({
  fullName: Yup.string()
    .min(1, "Name must be at least 1 characters")
    .max(60, "Name must not exceed 60 characters")
    .matches(/^[a-zA-Z\s]*$/, "1-60 char, no special symbols")
    .required("Input your name"),
  email: Yup.string().email().required("Input your email"),
  password: Yup.string()
    .min(8, "Password must be at least 8 characters")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*()_\-+={}[\]|:;"'<>,.?/]).{8,}$/,
      'Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character'
    )
    .required("Please enter your password"),
});
