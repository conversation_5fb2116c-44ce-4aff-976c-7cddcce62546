import React, { useEffect, useState } from "react";
import { Outlet, Navigate } from "react-router-dom";
import { handleCookies } from "./cookies";
import { useDispatch } from "react-redux";
import { setAuthData } from "../redux/features/authSlice";

function Auth() {
  const [isAuth, setIsAuth] = useState(null);
  const dispatch = useDispatch();

  useEffect(() => {
    const authCheck = async () => {
      const { token, user_name, user_role } = handleCookies.fetchCookies();

      if (!token) {
        setIsAuth(false);
      } else {
        // Don't handle permissions in Auth at all
        dispatch(
          setAuthData({
            token,
            user_name,
            user_role,
          }),
        );
        setIsAuth(true);
      }
    };

    authCheck();
  }, [dispatch]);

  if (isAuth === null) return null;

  return isAuth ? <Outlet /> : <Navigate to="/signin" />;
}

export default Auth;
