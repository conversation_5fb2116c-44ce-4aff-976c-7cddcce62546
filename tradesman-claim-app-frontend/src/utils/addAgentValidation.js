import * as Yup from 'yup';

export const addAgentValidation = Yup.object({
  agentName: Yup.string().required('Agent name is required'),
  host: Yup.string().required('Host field is required'),
  port: Yup.string().matches(/^[0-9]+$/, "Must be only digits").min(2, "Must be more than 1 character")
    .max(4, "Must be less than 4 characters").required('Port number is required'),
  userName: Yup.string().matches(/^\S+$/, "Spaces are not allowed").required('User name is required'),
  uuid: Yup.string().required('UUID is required'),
  publickey: Yup.string().required('Public key is required')
});