import * as Yup from "yup";

export const resetPasswordSchema = Yup.object({
    password: Yup.string()
    .min(8, '8+ characters, with min. one number, one uppercase letter and one special character')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*()_\-+={}[\]|:;"'<>,.?/]).{8,}$/,
    '8+ characters, with min. one number, one uppercase letter and one special character'
  ).required("Please enter your password"),
    passwordConfirmation: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .oneOf([Yup.ref('password'), null], 'The password confirmation does not match.')
    .required("Please confirm your password")
});
