
export const monthlyPriceObj = [
  {
    id: 1,
    title: 'Lite',
    tag: 'Monthly',
    price: '$75',
    priceDescription: '/month',
    pricedetails: ['Up to 10 Tables', '$7.50 per table'],
    keybenefits: [
      { benefit: 'Lite Dashboard', disable: false },
      { benefit: 'Query Archive Data', disable: true },
      { benefit: 'Storage Cost', disable: true },
      { benefit: 'DB Storage Cost Savings', disable: true },
      { benefit: 'Project Table Growth', disable: true },
      { benefit: 'Table Size + Indexing', disable: true },
    ]
  },
  {
    id: 2,
    title: 'Medium',
    tag: 'Monthly',
    price: '$295',
    priceDescription: '/month',
    pricedetails: ['Up to 50 Tables', '$5.90 per table'],
    keybenefits: [
      { benefit: 'Dashboard', disable: false },
      { benefit: 'Query Archive Data', disable: false },
      { benefit: 'Storage Cost', disable: false },
      { benefit: 'DB Storage Cost Savings', disable: false },
      { benefit: 'Project Table Growth', disable: false },
      { benefit: 'Table Size + Indexing', disable: true },
    ]
  },
  {
    id: 3,
    title: 'High',
    tag: 'Monthly',
    price: '$1,999',
    priceDescription: '/month',
    pricedetails: ['Up to 500 Tables', '$4.00 per table'],
    keybenefits: [
      { benefit: 'Dashboard', disable: false },
      { benefit: 'Query Archive Data', disable: false },
      { benefit: 'Storage Cost', disable: false },
      { benefit: 'DB Storage Cost Savings', disable: false },
      { benefit: 'Project Table Growth', disable: false },
      { benefit: 'Table Size + Indexing', disable: false },
    ]
  },
  {
    id: 4,
    title: 'Enterprise',
    tag: 'Monthly',
    price: 'Custom',
    priceDescription: '/yearly billing only',
    pricedetails: ['Unlimited number of tables',],
    keybenefits: [
      { benefit: 'Dashboard', disable: false },
      { benefit: 'Query Archive Data', disable: false },
      { benefit: 'Storage Cost', disable: false },
      { benefit: 'DB Storage Cost Savings', disable: false },
      { benefit: 'Project Table Growth', disable: false },
      { benefit: 'Table Size + Indexing', disable: false },
    ]
  }
];



export const yearlyPriceObj = [
  {
    id: 5,
    title: 'Lite',
    tag: 'Yearly',
    price: '$900',
    priceDescription: '/year',
    pricedetails: ['Up to 10 Tables', '$7.50 per table'],
    keybenefits: [
      { benefit: 'Lite Dashboard', disable: false },
      { benefit: 'Query Archive Data', disable: true },
      { benefit: 'Storage Cost', disable: true },
      { benefit: 'DB Storage Cost Savings', disable: true },
      { benefit: 'Project Table Growth', disable: true },
      { benefit: 'Table Size + Indexing', disable: true },
    ]
  },
  {
    id: 6,
    title: 'Medium',
    tag: 'Yearly',
    price: '$3,240',
    priceDescription: '/year',
    pricedetails: ['Up to 50 Tables', '$5.90 per table'],
    keybenefits: [
      { benefit: 'Dashboard', disable: false },
      { benefit: 'Query Archive Data', disable: false },
      { benefit: 'Storage Cost', disable: false },
      { benefit: 'DB Storage Cost Savings', disable: false },
      { benefit: 'Project Table Growth', disable: false },
      { benefit: 'Table Size + Indexing', disable: true },
    ]
  },
  {
    id: 7,
    title: 'High',
    tag: 'Yearly',
    price: '$23,988',
    priceDescription: '/year',
    pricedetails: ['Up to 500 Tables', '$4.00 per table'],
    keybenefits: [
      { benefit: 'Dashboard', disable: false },
      { benefit: 'Query Archive Data', disable: false },
      { benefit: 'Storage Cost', disable: false },
      { benefit: 'DB Storage Cost Savings', disable: false },
      { benefit: 'Project Table Growth', disable: false },
      { benefit: 'Table Size + Indexing', disable: false },
    ]
  },
  {
    id: 8,
    title: 'Enterprise',
    tag: 'Yearly',
    price: 'Custom',
    priceDescription: '/yearly billing only',
    pricedetails: ['Unlimited number of tables',],
    keybenefits: [
      { benefit: 'Dashboard', disable: false },
      { benefit: 'Query Archive Data', disable: false },
      { benefit: 'Storage Cost', disable: false },
      { benefit: 'DB Storage Cost Savings', disable: false },
      { benefit: 'Project Table Growth', disable: false },
      { benefit: 'Table Size + Indexing', disable: false },
    ]
  }
];


export const freeTrailDetails = [
  "Run only 1 archiving job/day",
  "Archive 3 tables/day"
];

export const navMenu = [
  {
    name: 'Dashboard',
    link: '/'
  },
  {
    name: 'DB Servers',
    link: '/database-servers'
  },
  {
    name: 'Schedule',
    link: '/schedule'
  }
]