2025-07-31 10:20:43,361 INFO Set uid to user 0 succeeded
2025-07-31 10:20:43,364 INFO supervisord started with pid 1
2025-07-31 10:20:44,367 INFO spawned: 'react-dev' with pid 43
2025-07-31 10:20:44,369 INFO spawned: 'nginx' with pid 44
2025-07-31 10:20:45,560 INFO success: react-dev entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-07-31 10:20:45,560 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-07-31 10:24:39,435 WARN received SIGTERM indicating exit request
2025-07-31 10:24:39,436 INFO waiting for react-dev, nginx to die
2025-07-31 10:24:39,446 INFO stopped: nginx (exit status 0)
2025-07-31 10:24:40,447 INFO stopped: react-dev (terminated by SIGTERM)
2025-07-31 10:27:49,282 INFO Set uid to user 0 succeeded
2025-07-31 10:27:49,285 INFO supervisord started with pid 1
2025-07-31 10:27:50,288 INFO spawned: 'react-dev' with pid 42
2025-07-31 10:27:50,292 INFO spawned: 'nginx' with pid 43
2025-07-31 10:27:51,561 INFO success: react-dev entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-07-31 10:27:51,561 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-07-31 10:32:54,025 WARN received SIGTERM indicating exit request
2025-07-31 10:32:54,030 INFO waiting for react-dev, nginx to die
2025-07-31 10:32:54,047 INFO stopped: nginx (exit status 0)
2025-07-31 10:32:55,048 INFO stopped: react-dev (terminated by SIGTERM)
2025-07-31 10:39:01,934 INFO Set uid to user 0 succeeded
2025-07-31 10:39:01,937 INFO supervisord started with pid 1
2025-07-31 10:39:02,938 INFO spawned: 'react-dev' with pid 9
2025-07-31 10:39:02,941 INFO spawned: 'nginx' with pid 10
2025-07-31 10:39:04,234 INFO success: react-dev entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-07-31 10:39:04,234 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-07-31 10:41:23,159 WARN received SIGTERM indicating exit request
2025-07-31 10:41:23,159 INFO waiting for react-dev, nginx to die
2025-07-31 10:41:23,166 INFO stopped: nginx (exit status 0)
2025-07-31 10:41:24,167 INFO stopped: react-dev (terminated by SIGTERM)
2025-07-31 10:45:13,367 INFO Set uid to user 0 succeeded
2025-07-31 10:45:13,369 INFO supervisord started with pid 1
2025-07-31 10:45:14,379 INFO spawned: 'react-dev' with pid 9
2025-07-31 10:45:14,384 INFO spawned: 'nginx' with pid 10
2025-07-31 10:45:15,587 INFO success: react-dev entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-07-31 10:45:15,587 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
