version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: tradesman_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: tradesman_claim_app
      MYSQL_USER: tradesman_user
      MYSQL_PASSWORD: tradesman_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - tradesman_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot_password"]
      timeout: 20s
      retries: 10
      interval: 10s

  # phpMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: tradesman_phpmyadmin
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    ports:
      - "8181:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root_password
      MYSQL_ROOT_PASSWORD: root_password
    networks:
      - tradesman_network

  # Backend Laravel API
  backend:
    build:
      context: .
      dockerfile: backend.Dockerfile
    container_name: tradesman_backend
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    ports:
      - "9000:9000"  # PHP-FPM
      - "8000:80"    # Nginx for backend
    volumes:
      - ../tradesman-claim-app-backend:/var/www/html
      - ./backend/nginx.conf:/etc/nginx/sites-available/default
      - ./backend/php-fpm.conf:/etc/php/8.3/fpm/pool.d/www.conf
    environment:
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=tradesman_claim_app
      - DB_USERNAME=tradesman_user
      - DB_PASSWORD=tradesman_password
    networks:
      - tradesman_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      timeout: 10s
      retries: 5
      interval: 30s

  # Frontend React App with Nginx
  frontend:
    build:
      context: .
      dockerfile: frontend.Dockerfile
    container_name: tradesman_frontend
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    ports:
      - "3000:3000"  # React dev server
      - "80:80"      # Nginx frontend
    volumes:
      - ../tradesman-claim-app-frontend:/app
      - ./frontend/nginx.conf:/etc/nginx/sites-available/default
      - /app/node_modules  # Anonymous volume for node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api
      - CHOKIDAR_USEPOLLING=true
    networks:
      - tradesman_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      timeout: 10s
      retries: 5
      interval: 30s

volumes:
  mysql_data:

networks:
  tradesman_network:
    driver: bridge
