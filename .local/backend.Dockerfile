FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update -y && \
    apt-get install -y \
    software-properties-common \
    curl \
    wget \
    unzip \
    zip \
    git \
    supervisor \
    nginx \
    mysql-client \
    && add-apt-repository ppa:ondrej/php -y \
    && apt-get update -y

# Install PHP 8.3 and extensions
RUN apt-get install -y \
    php8.3 \
    php8.3-fpm \
    php8.3-mysql \
    php8.3-cli \
    php8.3-zip \
    php8.3-xml \
    php8.3-mbstring \
    php8.3-curl \
    php8.3-gd \
    php8.3-bcmath \
    php8.3-intl \
    php8.3-soap \
    php8.3-xsl \
    php8.3-sqlite3 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Install Node.js and npm
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Install PM2 globally
RUN npm install -g pm2

# Create www-data user directories
RUN mkdir -p /var/www/html \
    && chown -R www-data:www-data /var/www/html

# Copy PHP-FPM configuration
COPY backend/php-fpm.conf /etc/php/8.3/fpm/pool.d/www.conf

# Copy Nginx configuration
COPY backend/nginx.conf /etc/nginx/sites-available/default

# Remove default nginx site
RUN rm -f /etc/nginx/sites-enabled/default \
    && ln -s /etc/nginx/sites-available/default /etc/nginx/sites-enabled/

# Copy supervisor configuration
COPY backend/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Copy startup script
COPY backend/start.sh /start.sh
RUN chmod +x /start.sh

# Set working directory
WORKDIR /var/www/html

# Expose ports
EXPOSE 80 9000

# Start services
CMD ["/start.sh"]
