#!/bin/bash
set -e

echo "Starting Laravel Backend Setup..."

# Wait for MySQL to be ready
echo "Waiting for MySQL to be ready..."
while ! mysqladmin ping -h mysql -u tradesman_user -ptradesman_password --silent; do
    echo "Waiting for MySQL..."
    sleep 2
done
echo "MySQL is ready!"

# Change to Laravel directory
cd /var/www/html

# Set proper permissions
chown -R www-data:www-data /var/www/html
chmod -R 755 /var/www/html
chmod -R 775 storage bootstrap/cache

# Create .env file from .env.example if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file from .env.example..."
    cp .env.example .env
    
    # Update database configuration
    sed -i 's/DB_CONNECTION=sqlite/DB_CONNECTION=mysql/' .env
    sed -i 's/# DB_HOST=127.0.0.1/DB_HOST=mysql/' .env
    sed -i 's/# DB_PORT=3306/DB_PORT=3306/' .env
    sed -i 's/# DB_DATABASE=laravel/DB_DATABASE=tradesman_claim_app/' .env
    sed -i 's/# DB_USERNAME=root/DB_USERNAME=tradesman_user/' .env
    sed -i 's/# DB_PASSWORD=/DB_PASSWORD=tradesman_password/' .env
    
    # Set app URL
    sed -i 's|APP_URL=http://localhost|APP_URL=http://localhost:8080|' .env
fi

# Install Composer dependencies
echo "Installing Composer dependencies..."
composer install --no-dev --optimize-autoloader

# Generate application key if not set
if ! grep -q "APP_KEY=base64:" .env; then
    echo "Generating application key..."
    php artisan key:generate
fi

# Clear configuration and cache (skip cache:clear for now)
echo "Clearing configuration..."
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Wait a bit more for MySQL to be fully ready
sleep 5

# Test database connection
echo "Testing database connection..."
php artisan tinker --execute="DB::connection()->getPdo();" || {
    echo "Database connection failed, waiting longer..."
    sleep 10
    php artisan tinker --execute="DB::connection()->getPdo();" || {
        echo "Database still not ready, but continuing..."
    }
}

# Run database migrations
# echo "Running database migrations..."
# php artisan migrate --force || {
#     echo "Migration failed, but continuing..."
# }

# Now clear cache after database is set up
echo "Clearing cache after database setup..."
php artisan cache:clear || {
    echo "Cache clear failed, but continuing..."
}

# Run database seeders (only if migrations succeeded)
# echo "Running database seeders..."
# php artisan db:seed --force || {
#     echo "Seeding failed, but continuing..."
# }

# Cache configuration (skip if database issues)
echo "Caching configuration..."
php artisan config:cache || echo "Config cache failed, continuing..."
php artisan route:cache || echo "Route cache failed, continuing..."
php artisan view:cache || echo "View cache failed, continuing..."

echo "Laravel setup completed!"

# Start supervisor
echo "Starting services..."
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
