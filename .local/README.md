# Tradesman Claim App - Local Development Environment

This Docker setup provides a complete local development environment for the Tradesman Claim App with:

- **MySQL 8.0** database
- **Laravel Backend** (PHP 8.3 + Nginx + PHP-FPM)
- **React Frontend** (Node.js 18 + Nginx reverse proxy)

## Prerequisites

- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- Source code for both backend and frontend applications

## Quick Start

1. **Navigate to the .local directory:**
   ```bash
   cd .local
   ```

2. **Start the development environment:**
   ```bash
   docker-compose up --build
   ```

3. **Access the applications:**
   - **Frontend**: http://localhost (React app with reverse proxy)
   - **Backend API**: http://localhost:8080 (Direct Laravel API access)
   - **MySQL**: localhost:3306

## Services Overview

### MySQL Database
- **Container**: `tradesman_mysql`
- **Port**: 3306
- **Database**: `tradesman_claim_app`
- **Username**: `tradesman_user`
- **Password**: `tradesman_password`
- **Root Password**: `root_password`

### Backend (Laravel API)
- **Container**: `tradesman_backend`
- **Ports**: 
  - 8080 (Nginx - direct API access)
  - 9000 (PHP-FPM)
- **Features**:
  - Auto-creates `.env` from `.env.example`
  - Runs database migrations and seeders
  - Installs Composer dependencies
  - Configured with PHP 8.3 and all required extensions

### Frontend (React App)
- **Container**: `tradesman_frontend`
- **Ports**:
  - 80 (Nginx - main access point)
  - 3000 (React dev server)
- **Features**:
  - Runs React in development mode (`npm start`)
  - Hot reload enabled
  - Nginx reverse proxy for API calls
  - Auto-installs npm dependencies

## Development Workflow

### Making Changes

1. **Backend Changes**: Edit files in `../tradesman-claim-app-backend/`
   - Changes are automatically reflected (volume mounted)
   - Laravel will auto-reload on file changes

2. **Frontend Changes**: Edit files in `../tradesman-claim-app-frontend/`
   - React hot reload will automatically update the browser
   - Changes are immediately visible

### Database Operations

```bash
# Access MySQL directly
docker exec -it tradesman_mysql mysql -u tradesman_user -ptradesman_password tradesman_claim_app

# Run Laravel migrations
docker exec -it tradesman_backend php artisan migrate

# Run seeders
docker exec -it tradesman_backend php artisan db:seed

# Clear cache
docker exec -it tradesman_backend php artisan cache:clear
```

### Logs and Debugging

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f mysql

# Access container shell
docker exec -it tradesman_backend bash
docker exec -it tradesman_frontend bash
```

## Stopping the Environment

```bash
# Stop all services
docker-compose down

# Stop and remove volumes (WARNING: This will delete database data)
docker-compose down -v
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Make sure ports 80, 3000, 3306, and 8080 are not in use
2. **Permission issues**: The containers run as www-data, file permissions are handled automatically
3. **Database connection**: Wait for MySQL health check to pass before backend starts

### Reset Everything

```bash
# Stop and remove everything
docker-compose down -v --rmi all

# Rebuild from scratch
docker-compose up --build
```

## File Structure

```
.local/
├── docker-compose.yml          # Main orchestration file
├── backend.Dockerfile          # Backend container definition
├── frontend.Dockerfile         # Frontend container definition
├── backend/
│   ├── nginx.conf              # Backend Nginx configuration
│   ├── php-fpm.conf           # PHP-FPM pool configuration
│   ├── supervisord.conf       # Process management
│   └── start.sh               # Backend startup script
├── frontend/
│   ├── nginx.conf              # Frontend Nginx with reverse proxy
│   ├── supervisord.conf       # Process management
│   └── start.sh               # Frontend startup script
└── mysql/
    └── init/
        └── 01-init.sql         # Database initialization
```

## Environment Variables

The setup automatically configures the following environment variables:

### Backend (.env)
- `DB_CONNECTION=mysql`
- `DB_HOST=mysql`
- `DB_DATABASE=tradesman_claim_app`
- `DB_USERNAME=tradesman_user`
- `DB_PASSWORD=tradesman_password`

### Frontend
- `REACT_APP_API_URL=http://localhost/api`
- `CHOKIDAR_USEPOLLING=true` (for file watching in Docker)

## Production Considerations

This setup is designed for **development only**. For production deployment:

1. Use production-optimized images
2. Enable HTTPS/SSL
3. Use environment-specific configurations
4. Implement proper security measures
5. Use managed database services
6. Build React app for production serving
