# Docker Environment Configuration for Tradesman Claim App
# This file contains the environment variables used by docker-compose

# MySQL Database Configuration
MYSQL_ROOT_PASSWORD=root_password
MYSQL_DATABASE=tradesman_claim_app
MYSQL_USER=tradesman_user
MYSQL_PASSWORD=tradesman_password

# Backend Configuration
BACKEND_PORT=8080
PHP_FPM_PORT=9000

# Frontend Configuration
FRONTEND_PORT=80
REACT_DEV_PORT=3000

# Network Configuration
NETWORK_NAME=tradesman_network

# Volume Configuration
MYSQL_DATA_VOLUME=mysql_data

# Development Settings
CHOKIDAR_USEPOLLING=true
REACT_APP_API_URL=http://localhost/api
