Bacend not being servered with followign errror

disable nginx on backend, serve app using php artisan serve on container start.
backend container app need to be accessed from localhost:8000 showing laravel starting page

frontend has error with .env REACT_APP_API_URL variable it should be http://localhost:8000/api
.env is not created 


backend error:
tradesman_backend     | Laravel setup completed!
tradesman_backend     | Starting services...
tradesman_backend     | 2025-07-31 10:44:51,436 INFO Set uid to user 0 succeeded
tradesman_backend     | 2025-07-31 10:44:51,438 INFO supervisord started with pid 1
tradesman_backend     | 2025-07-31 10:44:52,441 INFO spawned: 'php-fpm' with pid 58
tradesman_backend     | 2025-07-31 10:44:52,444 INFO spawned: 'nginx' with pid 59
tradesman_backend     | [31-Jul-2025 10:44:52] ERROR: unable to bind listening socket for address '/run/php/php8.3-fpm.sock': No such file or directory (2)
tradesman_backend     | [31-Jul-2025 10:44:52] ERROR: FPM initialization failed
tradesman_backend     | 2025-07-31 10:44:52,526 INFO exited: php-fpm (exit status 78; not expected)
tradesman_backend     | 2025-07-31 10:44:53,528 INFO spawned: 'php-fpm' with pid 68
tradesman_backend     | 2025-07-31 10:44:53,529 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
tradesman_backend     | [31-Jul-2025 10:44:53] ERROR: unable to bind listening socket for address '/run/php/php8.3-fpm.sock': No such file or directory (2)
tradesman_backend     | [31-Jul-2025 10:44:53] ERROR: FPM initialization failed
tradesman_backend     | 2025-07-31 10:44:53,596 INFO exited: php-fpm (exit status 78; not expected)
tradesman_backend     | 2025-07-31 10:44:55,599 INFO spawned: 'php-fpm' with pid 69
tradesman_backend     | [31-Jul-2025 10:44:55] ERROR: unable to bind listening socket for address '/run/php/php8.3-fpm.sock': No such file or directory (2)
tradesman_backend     | [31-Jul-2025 10:44:55] ERROR: FPM initialization failed
tradesman_backend     | 2025-07-31 10:44:55,675 INFO exited: php-fpm (exit status 78; not expected)
tradesman_backend     | 2025-07-31 10:44:58,680 INFO spawned: 'php-fpm' with pid 70
tradesman_backend     | [31-Jul-2025 10:44:58] ERROR: unable to bind listening socket for address '/run/php/php8.3-fpm.sock': No such file or directory (2)
tradesman_backend     | [31-Jul-2025 10:44:58] ERROR: FPM initialization failed
tradesman_backend     | 2025-07-31 10:44:58,749 INFO exited: php-fpm (exit status 78; not expected)
tradesman_backend     | 2025-07-31 10:44:59,750 INFO gave up: php-fpm entered FATAL state, too many start retries too quickly
