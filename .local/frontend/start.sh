#!/bin/bash
set -e

echo "Starting React Frontend Setup..."

# Change to app directory
cd /app

# Check if package.json exists
if [ ! -f package.json ]; then
    echo "Error: package.json not found in /app"
    echo "Make sure the frontend source code is mounted correctly"
    exit 1
fi

# Install npm dependencies if node_modules doesn't exist or is empty
if [ ! -d node_modules ] || [ -z "$(ls -A node_modules)" ]; then
    echo "Installing npm dependencies with legacy peer deps..."
    npm install --legacy-peer-deps
else
    echo "Node modules already installed, skipping npm install"
fi

#dumb REACT_APP_API_URL to .env
echo "REACT_APP_API_URL=http://localhost:8000/api" > .env

# Set proper permissions
chown -R www-data:www-data /app

echo "React frontend setup completed!"

# Start supervisor
echo "Starting services..."
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
