#!/bin/bash

# Tradesman Claim App - Development Helper Script

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Tradesman Claim App - Development Helper"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start       Start the development environment"
    echo "  stop        Stop the development environment"
    echo "  restart     Restart the development environment"
    echo "  rebuild     Rebuild and start the environment"
    echo "  logs        Show logs from all services"
    echo "  logs-be     Show backend logs"
    echo "  logs-fe     Show frontend logs"
    echo "  logs-db     Show database logs"
    echo "  shell-be    Access backend container shell"
    echo "  shell-fe    Access frontend container shell"
    echo "  shell-db    Access database container shell"
    echo "  migrate     Run Laravel migrations"
    echo "  seed        Run Laravel seeders"
    echo "  artisan     Run Laravel artisan command (e.g., ./dev-helper.sh artisan cache:clear)"
    echo "  npm         Run npm command in frontend (e.g., ./dev-helper.sh npm install)"
    echo "  reset       Reset everything (WARNING: Deletes all data)"
    echo "  status      Show container status"
    echo "  help        Show this help message"
    echo ""
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Main command handling
case "${1:-help}" in
    "start")
        log_info "Starting Tradesman Claim App development environment..."
        check_docker
        docker-compose up -d
        log_success "Environment started! Access the app at http://localhost"
        ;;
    
    "stop")
        log_info "Stopping development environment..."
        docker-compose down
        log_success "Environment stopped."
        ;;
    
    "restart")
        log_info "Restarting development environment..."
        docker-compose down
        docker-compose up -d
        log_success "Environment restarted!"
        ;;
    
    "rebuild")
        log_info "Rebuilding and starting development environment..."
        check_docker
        docker-compose down
        docker-compose up --build -d
        log_success "Environment rebuilt and started!"
        ;;
    
    "logs")
        docker-compose logs -f
        ;;
    
    "logs-be")
        docker-compose logs -f backend
        ;;
    
    "logs-fe")
        docker-compose logs -f frontend
        ;;
    
    "logs-db")
        docker-compose logs -f mysql
        ;;
    
    "shell-be")
        log_info "Accessing backend container shell..."
        docker exec -it tradesman_backend bash
        ;;
    
    "shell-fe")
        log_info "Accessing frontend container shell..."
        docker exec -it tradesman_frontend bash
        ;;
    
    "shell-db")
        log_info "Accessing database container shell..."
        docker exec -it tradesman_mysql mysql -u tradesman_user -ptradesman_password tradesman_claim_app
        ;;
    
    "migrate")
        log_info "Running Laravel migrations..."
        docker exec -it tradesman_backend php artisan migrate
        log_success "Migrations completed!"
        ;;
    
    "seed")
        log_info "Running Laravel seeders..."
        docker exec -it tradesman_backend php artisan db:seed
        log_success "Seeders completed!"
        ;;
    
    "artisan")
        if [ -z "$2" ]; then
            log_error "Please provide an artisan command. Example: $0 artisan cache:clear"
            exit 1
        fi
        shift
        log_info "Running: php artisan $*"
        docker exec -it tradesman_backend php artisan "$@"
        ;;
    
    "npm")
        if [ -z "$2" ]; then
            log_error "Please provide an npm command. Example: $0 npm install"
            exit 1
        fi
        shift
        log_info "Running: npm $*"
        docker exec -it tradesman_frontend npm "$@"
        ;;
    
    "reset")
        log_warning "This will delete all data and rebuild everything!"
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "Resetting development environment..."
            docker-compose down -v --rmi all
            docker-compose up --build -d
            log_success "Environment reset and rebuilt!"
        else
            log_info "Reset cancelled."
        fi
        ;;
    
    "status")
        log_info "Container status:"
        docker-compose ps
        ;;
    
    "help"|*)
        show_usage
        ;;
esac
