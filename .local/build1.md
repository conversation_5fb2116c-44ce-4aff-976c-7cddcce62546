Role: fullstack developer, docker expert, laravel exper, react expert

Task: build a local developemnt environment for tradesman-claim-app using docker and docker-compose for local development and testing. Following terraform specs 

Docker setup:
1- mysql db container
2- backend php-fpm container for api, api backend app is built with laravel
3- frontend nginx container for serving react app and reverse proxy to backend api

References:
[prod env setup](../tradesman-claim-app/infrastructure/Environment/Prod/tradesman-claim-app/scripts)
use to build similar nginx config and php-fpm

App setup
- in backend container image, add image setup step to create .env from .env.example and adjust the mysql connection to created mysql container. and run migrations and seeders with any other necassry laravel setup steps.

