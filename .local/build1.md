Role: fullstack developer, docker expert, laravel exper, react expert

Task: build a local developemnt environment for tradesman-claim-app using docker and docker-compose for local development and testing. Following terraform specs

Docker setup:
1- mysql db container
2- backend php-fpm container for api, api backend app is built with laravel
3- frontend nginx container for serving react app and reverse proxy to backend api

References:
[prod env setup](../tradesman-claim-app/infrastructure/Environment/Prod/tradesman-claim-app/scripts)
use to build similar nginx config and php-fpm

App setup
- in backend container image, add image setup step to create .env from .env.example and adjust the mysql connection to created mysql container. and run migrations and seeders with any other necassry laravel setup steps.

- server backend using php-fpm and nginx
- serve frontend pm2
- connect frontend to backend api using nginx reverse proxy
- do not build react app in dockerfile, instead mount the source code and run npm start to serve the app in dev mode.
- mount backend source code and run composer install and pm2 start

backend app path: /home/<USER>/workspace/DS/tradesman/tradesman-claim-app-backend
frontend app path: /home/<USER>/workspace/DS/tradesman/tradesman-claim-app-frontend

create all docker files and docker-compose file to run the app locally, create all files inside .local directory

## COMPLETED TASKS:
✅ Created docker-compose.yml with MySQL, Backend, and Frontend services
✅ Created backend Dockerfile with PHP 8.3, Composer, and Laravel setup
✅ Created frontend Dockerfile with Node.js and development setup
✅ Created nginx configuration for backend API
✅ Created nginx configuration for frontend with reverse proxy
✅ Created environment configuration and startup scripts