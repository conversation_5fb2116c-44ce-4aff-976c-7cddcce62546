FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update -y && \
    apt-get install -y \
    curl \
    wget \
    nginx \
    supervisor \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 18.x
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Install PM2 globally
RUN npm install -g pm2

# Create app directory
RUN mkdir -p /app \
    && chown -R www-data:www-data /app

# Copy Nginx configuration
COPY frontend/nginx.conf /etc/nginx/sites-available/default

# Remove default nginx site and enable our config
RUN rm -f /etc/nginx/sites-enabled/default \
    && ln -s /etc/nginx/sites-available/default /etc/nginx/sites-enabled/

# Copy supervisor configuration
COPY frontend/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Copy startup script
COPY frontend/start.sh /start.sh
RUN chmod +x /start.sh

# Set working directory
WORKDIR /app

# Expose ports
EXPOSE 80 3000

# Start services
CMD ["/start.sh"]
